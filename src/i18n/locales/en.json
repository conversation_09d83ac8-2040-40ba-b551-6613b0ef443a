{"{action}_internal_message_template": "{{action}} Site letter template", "activity.apply": "Apply now", "activity.goRecharge": "Recharge now", "add_game_maintenance": "Added game maintenance", "baseLayout_searchInput_placeholder": "keywords", "common_account": "account number", "common_account_format_error": "Account format error, please enter 6-20 alphanumeric characters", "common_action": "operate", "common_active": "Enable", "common_add": "New", "common_add_name": "Added {{name}}", "common_add_success": "New addition successful", "common_add_success_description": "New addition is successful, please remember your account and password", "common_addTime": "New time", "common_alert": "Remind", "common_all": "all", "common_amount_min_error": "Amount cannot be less than 0.01", "common_approve": "pass", "common_autoRefresh": "Automatic refresh", "common_back": "return", "common_betAmount": "Betting points", "common_cancel": "Cancel", "common_cancelled": "Canceled", "common_category": "Classification", "common_check": "Check", "common_clear": "Clear", "common_close": "closure", "common_completed": "Completed", "common_condition": "condition", "common_confirm": "Sure", "common_confirm_active_name": "Confirm to enable this {{name}}?", "common_confirm_approve_name": "Confirm to pass this {{name}}?", "common_confirm_cancel_maintenance": "Confirm to cancel maintenance?", "common_confirm_change_status_disable": "Confirm to deactivate this {{name}}?", "common_confirm_change_status_enable": "Confirm to enable this {{name}}?", "common_confirm_delete_name": "Confirm to delete this {{name}}?", "common_confirm_disable_name": "Confirm to deactivate this {{name}}?", "common_confirm_reject_name": "Confirm to reject this {{name}}?", "common_confirm_release_frozen": "Confirm that the recovery is frozen?", "common_confirm_withdraw_name": "Confirm to take back this {{name}}?", "common_copy": "clone", "common_create": "Establish", "common_currency": "currency", "common_currentPhoto": "Current diagram", "common_data": "material", "common_default_password": "Preset password", "common_delete": "delete", "common_delete_success": "Deleted", "common_detail": "Details", "common_disable": "Disable", "common_disabled": "Disabled", "common_download": "download", "common_draw": "and", "common_edit": "edit", "common_edit_name": "Edit {{name}}", "common_else": "other", "common_enable": "Enable", "common_enabled": "Enabled", "common_end": "Finish", "common_endTime": "End time", "common_export": "Data Export", "common_filtertest": "Filter test account", "common_first": "First", "common_hour": "Hour", "common_id": "ID", "common_inactive": "Disable", "common_info": "illustrate", "common_lastEditAdmin": "Last editing administrator", "common_lastEditTime": "Last editing time", "common_lastOperate": "Last operator/last operation time", "common_lastTime": "Last login time", "common_level": "grade", "common_lose": "lose", "common_max_value_error": "Please enter a number of 0~100", "common_min_value": "Minimum value is {{value}}", "common_min_value_error": "Please enter a number greater than 0", "common_newPassword": "New Password", "common_no": "no", "common_no_changes": "No changes were made", "common_none": "none", "common_note": "Remark", "common_note_max_length": "Note cannot exceed 500 characters", "common_onsave": "Settings are saved", "common_operation_failed": "Operation Failed", "common_operation_success": "Operation Success", "common_operator": "Operator", "common_order": "Sort", "common_pass": "pass", "common_pending": "Waiting", "common_permission": "Permissions", "common_please_enter": "Please enter {{name}}", "common_please_select": "Please select {{name}}", "common_preview": "Preview", "common_profit": "Profit and loss", "common_publish": "release", "common_read": "Review", "common_record": "Change record", "common_reject": "reject", "common_reload": "Reload", "common_required": "This field is required", "common_required_field": "This field is required", "common_reset": "Reset", "common_reset_otp": "Reset the GOOGLE verification code", "common_reset_password": "Reset Password", "common_result": "result", "common_save": "store", "common_save_draft": "Save drafts", "common_save_notice": "Please click Save to finish editing before your settings will be applied.", "common_search": "Query", "common_select": "Please select", "common_start": "start", "common_started": "rise", "common_startTime": "Start time", "common_status": "state", "common_submit": "confirm", "common_supplier": "supplier", "common_time": "time", "common_timeSelect": "Time filtering", "common_unknown": "unknown", "common_unpublish": "Removed", "common_unread": "Not read", "common_unsettled": "Waiting for prize draw", "common_until": "to", "common_updatedTime": "Update time", "common_upload": "Upload", "common_upload_failed": "Upload Failed", "common_upload_image": "Upload Image", "common_upload_success": "Upload Success", "common_view": "Check", "common_win": "win", "common_yes": "yes", "componenst_searchInput_placeholder": "Search for keywords", "components_audioUploader_description": "Only upload audio files!", "components_audioUploader_limit": "Files must be less than 5MB!", "components_audioUploader_upload": "Click to upload or drag and drop the file", "components_autoRefresh_interval_10s": "10 seconds", "components_autoRefresh_interval_300s": "300 seconds", "components_autoRefresh_interval_30s": "30 seconds", "components_autoRefresh_interval_60s": "60 seconds", "components_autoRefresh_interval_none": "Not refreshed", "components_autoRefresh_manual_refresh": "Manual refresh", "components_autoRefresh_second": "Second", "components_formTimePeriodSelect_publishType": "Release time", "components_formTimePeriodSelect_publishType_fixedTime": "Start timed", "components_formTimePeriodSelect_publishType_permanent": "permanent", "components_formTimePeriodSelect_publishType_timeRange": "Start-end", "components_gameBatchImageUploadModal_upload": "Drag the image here, or click Upload the file", "components_imageUploader_description": "Only upload {{fileType}} files!", "components_imageUploader_limit": "The file must be less than {{size}}MB!", "components_language_en": "English", "components_language_ja": "Japanese", "components_language_ko": "Korean language", "components_language_zh_tw": "Traditional Chinese", "components_quickDateSelect_custom": "Custom", "components_quickDateSelect_lastMonth": "Last month", "components_quickDateSelect_lastWeek": "Last week", "components_quickDateSelect_thisMonth": "this month", "components_quickDateSelect_thisWeek": "This week", "components_quickDateSelect_today": "today", "components_quickDateSelect_yesterday": "Yesterday", "components_resetOtpModal_description": "Are you sure to reset the GOOGLE verification code?", "components_resetOtpModal_success": "Reset successfully, please log in again", "components_resetPasswordModal_description": "Are you sure to reset your password?", "components_resetPasswordModal_success": "Reset successfully, please use the following password to log in and set a new password", "components_switchLanguage_chinese": "Traditional Chinese", "components_switchLanguage_english": "English", "components_uploader_image_load_error": "Image loading failed!", "components_uploader_image_too_large": "The image file must be less than 500KB!", "components_uploader_invalid_file_type": "Only upload pictures or video files!", "components_uploader_invalid_image_size": "The picture size is wrong!", "components_userInfoItem_changePassword": "Change Password", "components_userInfoItem_lastLogin": "Last Login", "components_userInfoItem_logout": "Logout", "error_input_decimal_places": "The number of decimal places must be {{decimalPlaces}} bits", "error_input_digit": "Please enter the number of {{digits}} digits", "error_input_length_limit": "The length must be {{min}}-{{max}} characters", "error_input_number": "Please enter a number", "error_input_required": "Please confirm whether the column is entered correctly", "game": "Game", "game_banner_picture": "Recommended horizontal version", "game_batch_upload": "Upload game pictures in batches", "game_batch_upload_rules": "Upload restrictions", "game_batch_upload_rules_amount": "Quantity: Up to 150 pictures", "game_batch_upload_rules_format": "Format: Webp", "game_batch_upload_rules_parse_rules": "Analytical rules", "game_batch_upload_rules_parse_rules_example": "Example: ATG_TABLE_$$117$$_ICON.png", "game_batch_upload_rules_parse_rules_format": "Format: Supplier_Category_$$GameID$$_Purpose.png", "game_batch_upload_rules_size": "Size: Single sheet <100kb", "game_batch_upload_rules_size_rules": "Analytical size", "game_categories": "Game Category Management", "game_category": "Game Category", "game_category_home": "front page", "game_category_name": "Classification", "game_conditions": "Startup conditions", "game_general_picture": "—General game pictures", "game_icon": "Game pictures", "game_icon_status": "Is the general gaming circle missing?", "game_image": "Game Image", "game_jp_support": "Support JP", "game_last_edit_admin": "Last edit admin/time", "game_layout_setting_description": "Please click Save to finish editing before your settings will be applied.", "game_layout_setting_title": "Game classification/{{type}} settings", "game_layout_sort_all_description": "Games in this area will always be displayed at the forefront of the entire game list", "game_layout_sort_all_title": "All games sorting", "game_layout_sort_mode_auto": "According to the number of gamers in the past seven days (updated at 00 a.m. every day)", "game_layout_sort_mode_description": "Set the sorting method of other games except for the top game", "game_layout_sort_mode_home_description": "Set the sorting method of other games except popular games", "game_layout_sort_mode_title": "Automatic sorting mode", "game_layout_sort_mode_unknown": "Not specifying mode", "game_layout_sort_top_description": "Games in this area will be displayed in popular game areas", "game_layout_sort_top_home_description": "Games in this area will be displayed in popular game areas", "game_layout_sort_top_home_title": "Popular Games Sorting", "game_layout_sort_top_title": "Top game sorting", "game_layout_table_category": "Classification", "game_layout_table_image": "Game pictures", "game_layout_table_name": "Game Name", "game_layout_table_provider": "supplier", "game_limit": "Game Limit", "game_limit_none": "No restrictions", "game_maintenance": "Game Maintenance Settings", "game_maintenance_mode": "Maintenance mode", "game_multi_support": "Support multiple openings", "game_name": "game", "game_name_filter": "Game name (fuzzy)", "game_order_game_category": "Game Category", "game_order_home_layout": "Homepage", "game_picture_preview": "Game map preview", "game_potrait_picture": "Recommended image for direct version", "game_provider": "supplier", "game_provider_id": "Vendor ID", "game_provider_last_edit_time_admin": "Last edit time/Last edit administrator", "game_provider_wallet_type": "Operation mode", "game_provider_wallet_type_single": "Single wallet", "game_provider_wallet_type_transfer": "Transfer wallet", "game_providers": "Supplier Management", "game_selection_disabled_tooltip": "This game has been selected and cannot be deselected", "game_selection_max_reached": "Maximum number of choices has been reached", "game_selection_modal_title": "Added top game", "game_selection_summary": "Selected: {{selected}} (Total {{total}} games)", "game_selection_summary_with_max": "Selected: <span class=\"text-red-400\">{{selected}}</span>/{{max}} (Total {{total}} games)", "game_start_conditions": "Startup conditions", "game_upload_game_image": "Upload game pictures", "game_upload_game_image_description": "Support mp4 (file size <5mb) and Webp (file size <100kb)", "game_upload_game_image_size": "Width {{width}}*Height {{height}}(px)", "games": "Game Management", "hooks_useCopy_copySuccess": "Copy successfully", "internal_letter_template": "Site letter template", "internal_letter_template_category": "Template classification", "internal_letter_template_content": "Letter content", "internal_letter_template_description": "Template description", "internal_letter_template_id": "Template ID", "internal_letter_template_title": "Letter title", "internal_letter_template_type": "Template details", "internal_letter_template_variable_setting": "Variable settings", "internal_letter_template_variable_setting_description": "You can click on the variable items you want to add before and after entering the text in the letter content on the left, and you can add them repeatedly.", "login": "<PERSON><PERSON>", "login_error_length": "Length must be 6-20 characters", "login_error_otp_failed": "Please enter a 6-digit verification code", "login_error_password_not_match": "Passwords are inconsistent", "login_error_password_required": "Please enter your password", "login_error_pattern": "Only letters and numbers are allowed", "login_error_username_required": "Please enter your account number", "login_label_account": "Account", "login_label_password": "Password", "login_otp": "Verification code", "login_otp_step1": "Download and install the \"Google 身份驗證器\" application", "login_otp_step2": "Click the Add Account button and scan the QR code above", "login_otp_step3": "Get the six-digit verification code from the APP above and fill in it", "login_otp_title": "Enter a 6-digit verification code", "login_placeholder_account": "Please enter your account", "login_placeholder_password": "Please enter your password", "login_reset_password": "Please reset the new password", "login_reset_password_label_confirmPassword": "Enter the new password again", "login_reset_password_label_newPassword": "Set a new password", "login_reset_password_label_oldPassword": "Please enter the old password", "login_reset_password_placeholder_confirmPassword": "Please enter the new password again", "login_reset_password_placeholder_password": "Please enter 6 to 20 English numerical symbols", "login_success": "<PERSON><PERSON> successfully", "logout": "Logout", "maintenance": "Maintenance", "maintenance_add_time_admin": "Added administrator/time", "maintenance_end_time": "Maintenance End Time", "maintenance_last_time_admin": "Last edit admin/time", "maintenance_mode": "Maintenance Mode", "maintenance_mode_all": "all", "maintenance_mode_cancel": "Cancel maintenance", "maintenance_mode_canceled": "Canceled", "maintenance_mode_emergency": "Emergency Maintenance", "maintenance_mode_ended": "Ended", "maintenance_mode_now": "Maintain transportation now", "maintenance_mode_schedule": "Determine time for transportation maintenance", "maintenance_mode_scheduled": "Scheduled", "maintenance_mode_undermaintenance": "In maintenance", "maintenance_mode_upgrade": "System Upgrade", "maintenance_start_time": "Maintenance Start Time", "maintenance_time": "Maintenance time", "maintenance_time_range": "Maintenance Time Range", "pages_403_title": "No permission to access this page", "pages_404_title": "This page cannot be found", "pages_500_title": "System maintenance, please try again later", "pages_activity": "Activity", "pages_activity_availableTags": "Can participate", "pages_activity_condition": "Activity conditions setting", "pages_activity_conduct_time": "Actual time of the event", "pages_activity_conduct_time_tooltip": "Actual effective time of the event", "pages_activity_confirm_unpublish": "Are you sure you will remove this event?", "pages_activity_CTA": "CTA/Activity Tips", "pages_activity_CTA_tooltip": "Activity progress guide", "pages_activity_feedback": "Feedback points", "pages_activity_feedback_ratio": "Feedback ratio (%)", "pages_activity_feedback_ratio_tooltip": "Recharge amount *Reward ratio = Gift amount", "pages_activity_feedback_upper_limit": "Maximum feedback points", "pages_activity_feedbackUpperLimit_info": "The highest feedback point is", "pages_activity_fridays": "Friday", "pages_activity_frozenTime": "Reward freezing time (units of hours, no limit on input table)", "pages_activity_gift": "Select a gift item", "pages_activity_giftCondition": "Reward conditions", "pages_activity_giftmoney": "Gift gift", "pages_activity_giftmoney_amount": "Gift mode", "pages_activity_giftmoney_amount_fixed": "Fixed points", "pages_activity_giftmoney_amount_ratio": "Proportion", "pages_activity_giftmoney_group": "Group", "pages_activity_giftmoney_multiple": "Range", "pages_activity_giftmoney_setting": "Gift Gift Setting", "pages_activity_giftmoney_single": "Single group", "pages_activity_info": "Activity introduction", "pages_activity_input_warning": "Please enter the upper limit", "pages_activity_isApply": "Show the application button", "pages_activity_isReview": "Whether to review", "pages_activity_joinLevel": "Limit participation level threshold", "pages_activity_joinMode": "Specify player tags (multiple choice)", "pages_activity_list": "Activity list", "pages_activity_list_discontinued": "Activities removed from the shelves", "pages_activity_max_must_gt_prev": "The upper limit must be greater than the previous set of upper limits", "pages_activity_min_singleInfo": "Minimum recharge required", "pages_activity_mondays": "Monday", "pages_activity_name": "Activity name", "pages_activity_needApply": "Required to apply", "pages_activity_not_include": "Not included", "pages_activity_order": "Activity sorting", "pages_activity_percent_info": "The discount points are the recharge points", "pages_activity_period": "Time of activity", "pages_activity_photo": "Activity map", "pages_activity_photo_hint": "Size recommendation: Width 558* Height 320(px). Images support PNG, JPG, Webp, file size <500KB; videos support mp4, apng, file size <5MB", "pages_activity_previous_input": "First group import", "pages_activity_prop": "Gift props", "pages_activity_publish_description": "The event cannot be edited after it is released", "pages_activity_publish_time": "Event launch time", "pages_activity_publish_time_tooltip": "The time when the event is displayed in the front desk", "pages_activity_read_title": "The height of the contents will vary depending on the browsing device", "pages_activity_saturdays": "Saturday", "pages_activity_sundays": "Sunday", "pages_activity_templateType": "Activity details", "pages_activity_thursdays": "Thursday", "pages_activity_topup_gap": "Recharge points range", "pages_activity_tuesdays": "Tuesday", "pages_activity_unavailableTags": "Not allowed to participate", "pages_activity_wednesdays": "Wednesday", "pages_activity_withdrawRewardTime": "Receive time limit (units of hours, calculation starts from the reward distribution to the reward center, input table 0 is not limited)", "pages_admin": "administrator", "pages_admin_addTimeFilter": "Added time filter", "pages_admin_confirm_status_disable": "Are you sure you have deactivated this account?", "pages_admin_confirm_status_enable": "Are you sure you enable this account?", "pages_admin_createdBy": "Added administrator", "pages_admin_password": "password", "pages_admin_register_way": "Registration method", "pages_admin_registerTimeFilter": "Registration time filter", "pages_admin_role": "Role", "pages_admin_updatedBy": "Last editing administrator", "pages_admin_username": "account number", "pages_agent_recordExport": "Data export record", "pages_agent_recordExport_abnormal": "abnormal", "pages_agent_recordExport_completed": "Completed", "pages_agent_recordExport_description": "Information security reminder: Administrators with permissions on this page can download data. Operators please delete files by themselves after using it. The system only retains export records for nearly 62 days", "pages_agent_recordExport_modalInfo": "Please go", "pages_agent_recordExport_modalInfo_content": "Page download", "pages_agent_recordExport_period": "Data interval", "pages_agent_recordExport_process": "Processing", "pages_agent_recordExport_title": "Data Export", "pages_announcement": "announcement", "pages_announcement_content": "Announcement content", "pages_announcement_title": "Announcement title", "pages_carousel": "Carousel", "pages_carousel_amount": "Carousel figure upper limit", "pages_carousel_bgImage": "Carousel background image", "pages_carousel_externalLink": "External link", "pages_carousel_externalLinkNote": "Users click on this carousel and will open a separate page to display the content of the URL", "pages_carousel_imageNote": "Support PNG, JPG, Webp, file size <1mb, width 750*400(px)>", "pages_carousel_interact": "Click to interact", "pages_carousel_internalLink": "Internal connection", "pages_carousel_internalLinkNote": "Display specific pages on the user's website/quickly start specific games", "pages_carousel_internalLinkNoteUsage": "How to use: query after copying the URL", "pages_carousel_name": "name", "pages_carousel_preview": "View carousel", "pages_carousel_previewImage": "Style preview", "pages_carousel_textImage": "Carousel text picture", "pages_contentCategory": "Copywriting category", "pages_contentCategory_description": "Notes on usage", "pages_contentCategory_description_placeholder": "Please enter a copywriting category description", "pages_contentCategory_key": "KEY", "pages_contentCategory_key_placeholder": "Please enter KEY", "pages_contentCategory_list": "Copywriting classification management", "pages_contentCategory_management": "Copywriting classification management", "pages_contentCategory_name": "Classification name (identification)", "pages_contentCategory_name_placeholder": "Please enter the copywriting category name", "pages_errorBoundary_title": "Network abnormality, please reorganize the page", "pages_frontContent": "Case Study", "pages_frontContent_category": "Copywriting category", "pages_frontContent_content": "Copywriting content", "pages_frontContent_title": "Copywriting title", "pages_game_account": "Game account (accurate)", "pages_game_accountUser": "Game Account", "pages_game_gameOrder": "Overview of the note list", "pages_game_gameOrder_Amount_total": "All betting points", "pages_game_gameOrder_betContent": "Betting content/details", "pages_game_gameOrder_pageAmount": "Betting points on this page", "pages_game_gameOrder_pageProfit": "The main points of profit and loss on this page", "pages_game_gameOrder_pageProfit_total": "All profit and loss points", "pages_game_gameOrder_status": "Bet status", "pages_game_gameOrder_status_settled": "Settled", "pages_game_gameOrder_status_unsettled": "Not settled", "pages_game_gameOrder_total": "Total bet odd numbers", "pages_game_gametype_arcade": "Arcade Games", "pages_game_gametype_fish": "Fishing", "pages_game_gametype_slot": "Video Games", "pages_game_gametype_table": "Chess and card games", "pages_game_id": "Game number", "pages_game_matchId": "Bureau number", "pages_game_matchId_placeholder": "Please enter the bureau number", "pages_game_name": "Game Management", "pages_game_order_tooltip": "- Game-related betting, payment, profit and loss data are the following bet times or update times as filters. - If you want the game information to change, please use the update time as the filter for the game information.", "pages_game_orderId": "Game number (accurate)", "pages_game_orderTime": "Betting time", "pages_game_updateTime": "Update time", "pages_giftSetting_basic": "Basic settings", "pages_giftSetting_giftFeeRate": "Points gift fee %", "pages_giftSetting_giftQuotas": "Daily gift amount", "pages_giftSetting_giftTimes": "Number of gifts per day", "pages_giftSetting_giveGiftVipLevel": "Minimum gift-gift VIP level", "pages_giftSetting_minGiftAmount": "Minimum number of points for a single transaction", "pages_giftSetting_minReceiveVipLevel": "Minimum gift-acceptable VIP level", "pages_giftSetting_minSendVipLevel": "Minimum gift-gift VIP level", "pages_giftSetting_receiveGiftFrozenRemainTimeLimit": "Freeze time for gift points", "pages_giftSetting_receiveGiftFrozenRemainTimeLimit_description": "The freezing time of the main points obtained by receiving gifts. Enter 0 to indicate that it does not freeze.", "pages_giftSetting_receiveGiftTimeLimit": "Receive gift time limit", "pages_giftSetting_receiveGiftTimeLimit_description": "Start calculating after completing OTP on the gift party, enter the 0 table without limit", "pages_giftSetting_receiveGiftVipLevel": "Minimum gift-acceptable VIP level", "pages_giftSetting_receivePointFreezeTime": "Freeze time for gift points", "pages_giftSetting_receivePointFreezeTime_description": "The unit is hours, the main points obtained by receiving gifts are frozen. Enter 0 to indicate that it does not freeze.", "pages_giftSetting_receiveTimeLimit": "Receive gift time limit", "pages_giftSetting_receiveTimeLimit_description": "The unit is hours, and the calculation starts after the gift party completes the OTP. Entering the 0 table is not restricted.", "pages_giftSetting_reserveAmount": "Minimum reserved points", "pages_giftSetting_vip": "VIP level", "pages_giftSetting_vipLevelSetting": "Detailed settings for each VIP level", "pages_giftSetting_vipLevelSetting_description": "Enter 0 to indicate no limit/no handling fee", "pages_giftSetting_vipSetting": "Detailed settings for each VIP level", "pages_giftSetting_vipSetting_description": "Enter 0 to indicate no limit/no handling fee", "pages_log_content": "Operation content", "pages_log_operateTimeFilter": "Operation time filtering", "pages_log_opPage": "Operation page", "pages_log_record": "Operation record (fuzzy)", "pages_log_time": "Operation time", "pages_operation_internalLetterManagement_basicSettings": "Basic settings", "pages_operation_internalLetterManagement_customizeContent": "Customize the content of the website", "pages_operation_internalLetterManagement_isImportantLetter": "Is it an important letter", "pages_operation_internalLetterManagement_isRead": "Read status", "pages_operation_internalLetterManagement_manualSendLetter": "Send a message manually", "pages_operation_internalLetterManagement_operator_send_time": "Operator/Send time", "pages_operation_internalLetterManagement_readingTime": "Read time", "pages_operation_internalLetterManagement_receivePlayer": "Receiver player", "pages_operation_internalLetterManagement_receivePlayerList": "Receiver Player List", "pages_operation_internalLetterManagement_recipient": "Receiver's object", "pages_operation_internalLetterManagement_recipientSetting": "Receive object settings", "pages_operation_internalLetterManagement_recipientType": "Recipient Type", "pages_operation_internalLetterManagement_recordList": "Record list", "pages_operation_internalLetterManagement_scheduleList": "Manual sending - scheduled posting", "pages_operation_internalLetterManagement_scheduleTime": "Schedule time", "pages_operation_internalLetterManagement_selectExistingTemplate": "Select an existing template", "pages_operation_internalLetterManagement_sendImmediately": "Send now", "pages_operation_internalLetterManagement_sendTime": "Send time", "pages_operation_internalLetterManagement_status": "Letter status", "pages_operation_internalLetterManagement_template": "Letter template classification", "pages_operation_internalLetterManagement_title": "Letter title", "pages_operation_internalLetterManagement_type": "Template classification", "pages_operation_internalLetterManagement_type_detail": "Template details", "pages_operation_prop_add": "Added props", "pages_operation_prop_backpackExpandLimit": "Extended upper limit limit (input table 0 is not limited)", "pages_operation_prop_bindActivity": "Binding activity name", "pages_operation_prop_content": "Props instructions (displayed in front desk prop pop-up window)", "pages_operation_prop_content_placeholder": "Please enter the prop instructions or introduction", "pages_operation_prop_createdBy": "Creator/Send Time", "pages_operation_prop_currentSetting": "Current system time limit", "pages_operation_prop_disableInfo": "\"Disable props\" will immediately stop all props appearing in the game scene in the future, but \"does not affect\" the use of existing props players.", "pages_operation_prop_discountPercent": "Discount ratio setting", "pages_operation_prop_enableInfo": "\"Enable Props\" will immediately remove the disabling status of the prop, but \"does not affect\" the use of existing props players", "pages_operation_prop_goToActivity": "Go to Settings", "pages_operation_prop_icon": "Props icon thumbnail", "pages_operation_prop_key": "Prop ID", "pages_operation_prop_name": "Prop name", "pages_operation_prop_picture": "Props pictures", "pages_operation_prop_picture_description": "Supports PNG, JPG, Webp, file size <500KB, width 200*200 (px)", "pages_operation_prop_setting": "Player backpack settings", "pages_operation_prop_settings": "Prop settings", "pages_operation_prop_singleExpandPoints": "Single expansion points", "pages_operation_prop_status": "Props status", "pages_operation_prop_status_change_error_400003": "This prop has been bound to the activity, so it cannot be deactivated. If it needs to be deactivated, please modify the activity condition design first - free props", "pages_operation_prop_type": "Prop type", "pages_operation_prop_validityPeriodTimes": "Props use period (not limited to fill in the form 0)", "pages_operation_prop_validityPeriodTimes_error": "The use period of props shall not be less than the system's gift-receiving time limit", "pages_operation_rewardList_batchGiftGivingPreviewModal_activityName": "Activity name", "pages_operation_rewardList_batchGiftGivingPreviewModal_frozenTime": "Freeze time", "pages_operation_rewardList_batchGiftGivingPreviewModal_isValid": "Is it valid or not", "pages_operation_rewardList_batchGiftGivingPreviewModal_playerAccount": "Player account", "pages_operation_rewardList_batchGiftGivingPreviewModal_rewardItemId": "Prop ID", "pages_operation_rewardList_batchGiftGivingPreviewModal_rewardType": "Gift Type", "pages_operation_rewardList_batchGiftGivingPreviewModal_rewardValue": "quantity", "pages_operation_rewardList_batchGiftGivingPreviewModal_showOnlyInvalidData": "Only invalid information is displayed", "pages_operation_rewardList_batchGiftGivingPreviewModal_title": "List preview", "pages_operation_rewardList_batchGiftGivingPreviewModal_totalDataSummary": "There are {{total}} gift information in the file, of which {{valid}} pen is valid. If there are any invalid information, please refer to the notes in the form below.", "pages_operation_rewardList_giftGiving_backendRemark": "Background Notes", "pages_operation_rewardList_giftGiving_batchSending": "Bulk Send - File Upload", "pages_operation_rewardList_giftGiving_batchUploadInstructions": "Please download the sample document and upload it after editing in the format. When uploading the document, do not overwrite the column name information of the first column of the document.", "pages_operation_rewardList_giftGiving_clickToDownload": "Click to download", "pages_operation_rewardList_giftGiving_collectionMethodSetting": "Receive method settings", "pages_operation_rewardList_giftGiving_collectionTimeLimit": "Receive time limit", "pages_operation_rewardList_giftGiving_collectionTimeLimitEnterHour": "Receive time limit - Enter hours", "pages_operation_rewardList_giftGiving_collectionTimeLimitEnterSpecificTime": "Receive time limit - Enter the specified time", "pages_operation_rewardList_giftGiving_content": "Internal text", "pages_operation_rewardList_giftGiving_customizeContent": "Customize the content of the website", "pages_operation_rewardList_giftGiving_downloadSampleFile": "Download the sample file", "pages_operation_rewardList_giftGiving_enterActivityName": "Enter the activity name (name)", "pages_operation_rewardList_giftGiving_enterPlayerAccount": "Enter the send player account", "pages_operation_rewardList_giftGiving_freezeTime": "Reward freeze time (hours), input table 0 is not restricted", "pages_operation_rewardList_giftGiving_giftItemSetting": "Gift project settings", "pages_operation_rewardList_giftGiving_hours": "Hour", "pages_operation_rewardList_giftGiving_instantArrival": "Instant arrival", "pages_operation_rewardList_giftGiving_internalMessageContentPreview": "Preview of the text of the website", "pages_operation_rewardList_giftGiving_internalMessageRemark": "Site message notes (If there is a variable \"front desk note\" in the template, the input content will be displayed in the site message)", "pages_operation_rewardList_giftGiving_internalMessageTitle": "In-site beacon title", "pages_operation_rewardList_giftGiving_internalMessageTitlePreview": "Preview of the beacon title", "pages_operation_rewardList_giftGiving_invalidDataFound": "{{count}}Pen invalid information", "pages_operation_rewardList_giftGiving_mainPoints": "Main points", "pages_operation_rewardList_giftGiving_pleaseEnter": "Please enter", "pages_operation_rewardList_giftGiving_pleaseSelect": "Please select", "pages_operation_rewardList_giftGiving_preview": "Preview", "pages_operation_rewardList_giftGiving_quantity": "quantity", "pages_operation_rewardList_giftGiving_rewardCenter": "Reward Center", "pages_operation_rewardList_giftGiving_rewardType": "Reward Type", "pages_operation_rewardList_giftGiving_selectInternalMessageTemplate": "Select the website letter template", "pages_operation_rewardList_giftGiving_selectSendingMethod": "Select the send method", "pages_operation_rewardList_giftGiving_sendToInternalMessage": "Send to the site message", "pages_operation_rewardList_giftGiving_singleSending": "Single send", "pages_operation_rewardList_giftGiving_specificTime": "Specify time", "pages_operation_rewardList_giftGiving_timeLimitDescription": "The unit is hours, and the calculation starts from the reward distribution to the reward center", "pages_operation_rewardList_giftGiving_tool": "Props", "pages_operation_rewardList_giftGiving_toolType": "Prop type: name change card", "pages_operation_rewardList_giftGiving_uploadSampleFile": "Upload sample files", "pages_operation_rewardList_giftGiving_useExistingTemplate": "Select an existing template (recommended)", "pages_operation_unreviewedActiveOrders_account": "Account", "pages_operation_unreviewedActiveOrders_event_template": "Activity template", "pages_operation_unreviewedActiveOrders_orderTime": "Order number/application time", "pages_operation_unreviewedActiveOrders_rewards": "Reward content", "pages_operation_unreviewedActiveOrders_tags": "Player tags", "pages_operation_unreviewedActiveOrders_template_type": "Template Type", "pages_operation_unreviewedActiveOrders_title": "Activity name", "pages_platform_accountLogin": "Account password login", "pages_platform_content": "content", "pages_platform_description": "Please click Save to finish editing before your settings will be applied.", "pages_platform_general_description": "LINE customer service link", "pages_platform_general_freeEditName": "Change your nickname for free", "pages_platform_general_freeEditName_cost": "Each time you modify the required master points", "pages_platform_general_freeEditName_description": "After closing, players need to pay the main points to modify their nickname, and can set the number of free times separately.", "pages_platform_general_freeEditName_time": "Free modifications (Enter 0 means that each modification is charged)", "pages_platform_general_freeEditPhone": "Change the phone", "pages_platform_general_freeEditPhone_description": "After turning on, players can change their mobile phone numbers at the front desk.", "pages_platform_general_freeEditPhone_fee": "Each time you modify the required master points", "pages_platform_general_placeholder": "Please enter the link", "pages_platform_general_title": "General settings", "pages_platform_input_error": "Do not be empty", "pages_platform_item": "project", "pages_platform_lineLogin": "LINE Login", "pages_platform_loading_message": "Operation is in progress, please wait", "pages_platform_login_title": "Log in to register settings", "pages_platform_modal_content": "The system will not automatically store the changed information", "pages_platform_model_comfirm": "Are you sure to leave this page", "pages_platform_phoneLogin": "Mobile phone number login", "pages_platform_sound_backgroundMusic": "Front desk background music", "pages_platform_sound_backgroundMusic_description": "Loop play, pay attention to file size", "pages_platform_sound_clickCloseSound": "Click on the front desk to close/return to the sound effect", "pages_platform_sound_clickCloseSound_description": "Only play once, time limit is within 1 second", "pages_platform_sound_clickFailedSound": "Front desk failure prompt sound effect", "pages_platform_sound_clickFailedSound_description": "Only play once, time limit is within 2 seconds", "pages_platform_sound_clickHintSound": "The front desk prompts sound effects", "pages_platform_sound_clickHintSound_description": "Only play once, time limit is within 2 seconds", "pages_platform_sound_clickOpenSound": "Click on the front desk to turn on/switch the sound effects", "pages_platform_sound_clickOpenSound_description": "Only play once, time limit is within 1 second", "pages_platform_sound_limit": "Support mp3 sound file, file less than 5mb", "pages_platform_sound_title": "Sound Effect Management", "pages_platform_upload_success": "{{name}} Uploaded", "pages_player": "Players", "pages_player_account": "Account (accurate)", "pages_player_account_exist": "This account/nickname already exists, please re-enter", "pages_player_accountType": "Account Type", "pages_player_accountType_official": "Official account", "pages_player_accountType_test": "Test the account", "pages_player_backend_label": "Backstage", "pages_player_bindPlayerList": "Bind player list", "pages_player_getPlayerByPhone": "Bind the player", "pages_player_lastLoginIp": "Last login IP/time", "pages_player_mainPointRemain": "Main points balance", "pages_player_nickname": "Nick name", "pages_player_nickname_error": "Length 4-20 words, without spaces and symbols", "pages_player_nicknameSearch": "Nickname (precise)", "pages_player_parentAccount": "Superior account", "pages_player_parentAccountSearch": "Superior account (accurate)", "pages_player_phone": "phone number", "pages_player_phone_error": "Please enter a total of 9 digits starting with 9", "pages_player_phone_label": "cell phone", "pages_player_phoneSearch": "Mobile phone number (accurate)", "pages_player_playerTag": "Player tags", "pages_player_playerTag_add": "Added tags", "pages_player_playerTag_name": "Tag name", "pages_player_registerIp": "Register IP/time", "pages_player_registerWay": "Registration method", "pages_player_registerWay_backend": "New background", "pages_player_registerWay_line": "Line", "pages_player_registerWay_phoneRegister": "Mobile phone number registration", "pages_player_tag_exist": "Repeat the label name, please re-enter", "pages_player_vip_bindPhone": "Bind your mobile phone number", "pages_player_vip_bindPhone_tooltip": "This condition can only have a single level. To reset, please turn off the enable status first.", "pages_player_vip_bindPlayerList": "Bind player list", "pages_player_vip_button_levelSetting": "Level start and deactivation settings", "pages_player_vip_editor": "The last operator", "pages_player_vip_firstTopup": "The first recharge needs to be completed", "pages_player_vip_levelcondition": "Level conditions", "pages_player_vip_levelcondition_description": "1. Upgrade mode can be upgraded if one of the conditions is met. 2. If a value is entered 0, it means that this condition is not enabled.", "pages_player_vip_monthAccumulatedBet": "Monthly code (points)", "pages_player_vip_monthAccumulatedValue": "Monthly recharge", "pages_player_vip_noitce_options1": "Completely understand the scope of this operation", "pages_player_vip_noitce_options2": "All relevant players have been reported", "pages_player_vip_noitce_options3": "Related data has been backed up", "pages_player_vip_notice": "Things to note", "pages_player_vip_notice_description": "Reducing the VIP level upper limit will cause all players who exceed the new limit to automatically downgrade to the new set maximum level, and this operation is not traceable. Before increasing the VIP level upper limit, please make sure that all VIP level parameters that will be opened have been fully set, including but not limited to upgrade conditions and activity settings...", "pages_player_vip_notice_info": "If you have any questions, please consult the technical team first before proceeding.", "pages_player_vip_notice_title": "After sending out the settings, the system will immediately automatically adjust the VIP level of all players based on the player's current information (such as betting amount, recharge amount, etc.) to make them meet the latest conditions. Are you sure to store it?", "pages_player_vip_notice_warning": "Please check all precautions", "pages_player_vip_notice_warning_description": "Before doing this, make sure you:", "pages_player_vip_totalAccumulatedBet": "Career code", "pages_player_vip_totalAccumulatedValue": "Career total recharge", "pages_player_vip_vipLevel": "VIP level", "pages_player_vip_vipLevelSetting": "The highest open VIP level", "pages_player_vipLevel": "VIP level", "pages_player_vipModify": "Unlock Modify status", "pages_player_vipModify_description": "The level of this player has been designated by the administrator. After the Modify is released, the system will determine which level the user should belong to based on the user's current value (such as last month's recharge amount), and the current VIP level setting, and determine which level the user should belong to. The system will complete the change and determine the execution?", "pages_player_vipRecord": "VIP level change record", "pages_playerTag": "Label", "pages_playerTag_add": "Added tags", "pages_playerTag_name": "Tag name", "pages_playerTag_operationIP": "Operator/New Time", "pages_playerTag_permission": "Tag permissions", "pages_playerTag_Tag": "No label", "pages_playerTag_updatedBy": "Last operator/last operation time", "pages_role": "Role", "pages_role_admin": "Administrator", "pages_role_adminList": "Administrator list", "pages_role_baseSetting": "Basic settings", "pages_role_name": "Role name", "pages_role_permissionSetting": "Permission settings", "pages_transaction_accountGiftQuery_account": "Player account", "pages_transaction_accountGiftQuery_frozenAmount": "Points", "pages_transaction_accountGiftQuery_frozenCreatedAt": "Start freezing time", "pages_transaction_accountGiftQuery_frozenDetail": "Details", "pages_transaction_accountGiftQuery_frozenInfo": "List of frozen points", "pages_transaction_accountGiftQuery_frozenRelease": "Recover freeze", "pages_transaction_accountGiftQuery_frozenRelease_success": "Unfreeze", "pages_transaction_accountGiftQuery_frozenReleasedAt": "End freezing time", "pages_transaction_accountGiftQuery_frozenStatus": "state", "pages_transaction_accountGiftQuery_frozenTotal": "Total amount in freezing: {{total}} points", "pages_transaction_accountGiftQuery_frozenType": "project", "pages_transaction_accountGiftQuery_instruction": "Please enter the player account to query", "pages_transaction_accountGiftQuery_noData": "The player account does not exist or the query fails", "pages_transaction_accountGiftQuery_noFrozenRecord": "No freezing points", "pages_transaction_accountGiftQuery_playerInfo": "Player information", "pages_transaction_accountGiftQuery_playerName": "Player nickname", "pages_transaction_accountGiftQuery_quotaInfo": "Credit Information", "pages_transaction_accountGiftQuery_restoreFrozen": "Recover freeze", "pages_transaction_accountGiftQuery_todayAmount": "Daily gift points", "pages_transaction_accountGiftQuery_todayTimes": "Number of gifts per day", "pages_transaction_balanceAdjustment_account": "Player account (precise)", "pages_transaction_balanceAdjustment_afterBalance": "Balance After", "pages_transaction_balanceAdjustment_amount": "Adjustment Amount", "pages_transaction_balanceAdjustment_beforeBalance": "Balance Before", "pages_transaction_balanceAdjustment_changeType": "type", "pages_transaction_balanceAdjustment_changeType_decrease": "Reduced balance", "pages_transaction_balanceAdjustment_changeType_increase": "Increased balance", "pages_transaction_balanceAdjustment_createdAt": "Created Time", "pages_transaction_balanceAdjustment_createdBy": "Operations Administrator", "pages_transaction_balanceAdjustment_currency": "<PERSON><PERSON><PERSON><PERSON>", "pages_transaction_balanceAdjustment_frozenTime": "Frozen Time", "pages_transaction_balanceAdjustment_frozenTime_min_error": "Frozen time cannot be less than 0", "pages_transaction_balanceAdjustment_frozenTime_modal": "Freeze time (units of hours, input table 0 will not be frozen)", "pages_transaction_balanceAdjustment_note": "Background Notes", "pages_transaction_balanceAdjustment_note_placeholder": "Please enter background notes", "pages_transaction_balanceAdjustment_orderTime": "Order ID/Initiation time", "pages_transaction_balanceAdjustment_remain": "Remaining points", "pages_transaction_balanceAdjustment_targetAccount": "Gift account", "pages_transaction_balanceAdjustment_timeSelect": "Trading time filter", "pages_transaction_completedGiftOrder_note": "Note", "pages_transaction_completedGiftOrder_updatedBy": "Last Updated By", "pages_transaction_topupOrder_admin": "Audit administrator", "pages_transaction_topupOrder_agentAccount": "Agent line", "pages_transaction_topupOrder_giftId": "Order ID", "pages_transaction_topupOrder_manual": "Manual callback", "pages_transaction_topupOrder_manual_content": "Confirm to callback this order?", "pages_transaction_topupOrder_note": "Background Notes", "pages_transaction_topupOrder_orderId": "Order ID", "pages_transaction_topupOrder_orderTime": "Initiation time", "pages_transaction_topupOrder_pagesumup": "The amount passed on this page", "pages_transaction_topupOrder_pagesumup_points": "This page passes the main points", "pages_transaction_topupOrder_pagesumup_total": "All pass amount", "pages_transaction_topupOrder_pagesumup_total_points": "All pass the main points", "pages_transaction_topupOrder_points": "Purchase props", "pages_transaction_topupOrder_timeSelect": "Time filtering criteria", "pages_transaction_topupOrder_timeSelect_createdAt": "Application time", "pages_transaction_topupOrder_timeSelect_updatedAt": "Review time", "pages_transaction_topupOrder_topupCount": "Total times", "pages_transaction_topupOrder_topupCount_first": "first", "pages_transaction_topupOrder_totalOrders": "Pass number of orders", "pages_transaction_topupOrder_totalPlayer": "Total number of players", "pages_transaction_topupsetting_basic": "Basic settings", "pages_transaction_topupsetting_basicVipLevel": "Open VIP level", "pages_transaction_topupsetting_basicVipLevel_tooltip": "You need to reach this level to use the mall function.", "pages_transaction_topupsetting_categoryImage": "Classification diagram", "pages_transaction_topupsetting_categoryName": "Category Name", "pages_transaction_topupsetting_chanel": "Recharge channel settings", "pages_transaction_topupsetting_Channel": "aisle", "pages_transaction_topupsetting_Channel_description": "Front Desk Channel Description", "pages_transaction_topupsetting_Channel_info": "Channel diagram specification: support jpg, jpeg, png, webp, apng, file size <1mb, width 80*80 (px)", "pages_transaction_topupsetting_Channel_list": "Channel list", "pages_transaction_topupsetting_Channel_name": "Channel name", "pages_transaction_topupsetting_Channel_photo": "Channel diagram", "pages_transaction_topupsetting_Channel_tags": "Open player tags", "pages_transaction_topupsetting_Channel_topup_amount": "Get the main points", "pages_transaction_topupsetting_Channel_topup_buyprice": "Recharge amount", "pages_transaction_topupsetting_image_info": "Support jpg,jpeg,png,webp,apng,file size <1mb,width 80*80(px)", "pages_transaction_topupsetting_photo_warning": "Please upload the classification diagram", "pages_transaction_topupsetting_popular": "Popular recharge combination settings", "pages_transaction_topupsetting_popular_category": "Recharge classification", "pages_transaction_topupsetting_popular_channel": "Recharge pipeline", "pages_transaction_topupsetting_popular_default": "Preset style", "pages_transaction_topupsetting_popular_group": "Combination name", "pages_transaction_topupsetting_popular_highlight": "Highlight style", "pages_transaction_topupsetting_popular_info": "Combination diagram specification: support jpg, jpeg, png, webp, apng, file size <1mb, width 80*80 (px)", "pages_transaction_topupsetting_popular_isHighlight": "Is it Highlight", "pages_transaction_topupsetting_popular_item": "Recharge amount/conversion points", "pages_transaction_topupsetting_popular_name": "Popular recharge combinations", "pages_transaction_topupsetting_popular_pic": "Combination diagram", "pages_transaction_topupsetting_supply": "Supplier settings", "pages_transaction_topupsetting_supply_id": "Payment provider ID", "pages_transaction_topupsetting_supply_name": "Payment dealer name", "pages_transaction_topupsetting_topupFrozenRemainTimeLimit": "Freeze time for recharge points", "pages_transaction_topupsetting_topupFrozenRemainTimeLimit_description": "The main points freeze time obtained by recharge, enter 0 to indicate that it does not freeze.", "pages_transaction_transactionRecord_amount": "Points", "pages_transaction_transactionRecord_currency": "Coin number", "pages_transaction_transactionRecord_currency_main": "Main points", "pages_transaction_transactionRecord_currency_secondary": "Secondary Points", "pages_transaction_transactionRecord_remain": "Remaining points", "pages_transaction_transactionRecord_time": "Trading time", "pages_transaction_transactionRecord_type": "Gold flow type", "pages_transaction_transactionRecord_type_bet": "Three-party game betting", "pages_transaction_transactionRecord_type_giftReturn": "Gift return", "pages_transaction_transactionRecord_type_prize": "Three-party game award", "pages_transaction_transactionRecord_type_receiveGift": "Receive gifts", "pages_transaction_transactionRecord_type_sendFee": "Gift delivery fee", "pages_transaction_transactionRecord_type_sendGift": "Gift gift", "pages_transaction_transactionRecord_type_topup": "top up", "pages_transaction_unfinishGiftOrder_gift": "Gift Item", "pages_transaction_unfinishGiftOrder_giftAccount": "Gift Account", "pages_transaction_unfinishGiftOrder_giftAccount_nickname": "Gift Player Nickname", "pages_transaction_unfinishGiftOrder_giftAmount": "Total Times", "pages_transaction_unfinishGiftOrder_giftAmount_perDay": "Daily Times", "pages_transaction_unfinishGiftOrder_giftId": "Gift Detail ID", "pages_transaction_unfinishGiftOrder_orderTime": "Order ID / Initiation Time", "pages_transaction_unfinishGiftOrder_targetAccount": "Recipient Account", "pages_transaction_unfinishGiftOrder_targetAccount_nickname": "Recipient Player Nickname", "pages_transaction_unfinishGiftOrder_updateTime": "Last Update Time", "pages_transaction_unfinishGiftOrder_withdraw": "<PERSON><PERSON> With<PERSON>wal", "pages_transaction_unfinishGiftOrder_withdraw_modal_description": "This operation will forcibly withdraw this order. Gift items, handling fees, times, and amounts will be returned to the gift giver. Confirm backend withdrawal?", "pages_transaction_unfinishGiftOrder_withdraw_modal_note_label": "Backend Note", "pages_transaction_unfinishGiftOrder_withdraw_modal_note_placeholder": "Please enter", "pages_transaction_unfinishGiftOrder_withdraw_modal_title": "<PERSON><PERSON>", "placeholder_input": "Please enter", "placeholder_select": "Please select", "router_403": "No permission to access this page", "router_403_": "No permission to access this page", "router_agent": "System Management", "router_agent_admin": "Admin List", "router_agent_log": "Operation Records", "router_agent_platform": "Platform Management", "router_agent_recordExport": "Data export record", "router_agent_role": "Administrator role management", "router_game": "Game Management", "router_game_gameorder": "Overview of the note list", "router_game_layout": "Game layout settings", "router_game_management": "Game Management", "router_operation": "operations management", "router_operation_activity": "Activity Management", "router_operation_announcement": "Announcement Management", "router_operation_carousel": "Carousel diagram management", "router_operation_frontContent": "Front desk copywriting content management", "router_operation_gameSort": "Game sorting settings", "router_operation_internalLetterManagement": "Site message sending record", "router_operation_internalLetterTemplate": "Site letter template management", "router_operation_internalLetterTemplates": "Site letter template management", "router_operation_propManagement": "Props Management", "router_operation_rewardList": "Reward Center", "router_operation_sendInternalLetter": "Site message sending record", "router_operation_sendReward": "Manual gift", "router_operation_unreviewedActiveOrders": "Activity order not reviewed", "router_player": "Player Management", "router_player_playerlist": "Player List", "router_player_playerTag": "Tag management", "router_player_vipplayer": "VIP level setting", "router_transaction_accountGiftQuery": "Gift quota inquiry", "router_transaction_balanceAdjustment": "Balance Adjustment", "router_transaction_completedGiftOrder": "Completed Gift Orders", "router_transaction_giftSetting": "Gift settings", "router_transaction_topuporder": "Unfinished recharge order", "router_transaction_topuporderhistory": "Completed recharge order", "router_transaction_topupsetting": "Recharge/Mall Settings", "router_transaction_transactionRecord": "Funding details", "router_transaction_unfinishGiftOrder": "Unfinished Gift Orders", "select_game_category": "Classification", "select_game_name": "Game name (fuzzy)", "select_game_provider": "supplier"}