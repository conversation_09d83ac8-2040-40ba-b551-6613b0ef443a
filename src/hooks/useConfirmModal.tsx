import { App } from 'antd';

import InfoIcon from '@/assets/img/icon/info.svg?react';

export const useConfirmModal = () => {
  const { t } = useTranslation();
  const { modal } = App.useApp();

  const ConfirmModalBody = ({
    icon,
    content
  }: {
    icon: 'info' | React.ReactNode;
    content: string | React.ReactNode;
  }) => {
    return (
      <div className="flex flex-col items-center gap-2">
        {icon && (
          <div className="flex items-center gap-2 mb-4 fill-warning">
            <InfoIcon width={48} height={48} />
          </div>
        )}
        <div className="text-sm">{content}</div>
      </div>
    );
  };

  const confirmModal = ({
    title,
    content,
    icon = 'info',
    onOk,
    onCancel,
    okText,
    cancelText
  }: {
    title?: string;
    content: string | React.ReactNode;
    icon?: 'info' | React.ReactNode;
    onOk: () => void;
    onCancel?: () => void;
    okText?: string;
    cancelText?: string;
  }) => {
    const confirmConfig = {
      title: title || t('common_alert'),
      content: <ConfirmModalBody icon={icon} content={content} />,
      onOk,
      onCancel,
      okText,
      cancelText,
      centered: true,
      icon: null
    };
    return modal.confirm(confirmConfig);
  };
  return {
    confirmModal
  };
};

export default useConfirmModal;
