export type Prop = {
  id: number;
  type: number;
  key: string;
  contentMapping: {
    en: {
      photo: string | null;
    };
    zhCn: {
      photo: string | null;
    };
    zhTw: {
      photo: string | null;
      title: string;
      content: string;
    };
  };
  note: string;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
  typeLabel: string;
  status: number;
};

export type PropListParams = {
  page: number;
  limit: number;
  key?: string;
  type?: number;
  status?: number;
  name?: string;
};

export enum PropStatus {
  DISABLE = 0,
  ENABLE = 1,
  DELETE = 2
}
