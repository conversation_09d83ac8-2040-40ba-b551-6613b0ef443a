export type Activity = {
  id: number;
  order: number;
  templateType: number;
  contentMapping: {
    zhTw: {
      title: string;
      content: string;
      photo: string;
    };
  };
  shelfStartTime: number;
  shelfEndTime: number;
  conductType: number; // API取得所有活動實際進行時間類型
  conductWeek?: string;
  conductStartTime: number;
  conductEndTime: number;
  status: number; // API取得所有活動狀態類型
  needApply: 0 | 1;
  isReview: 0 | 1;
  openCta: 0 | 1;
  joinLevel?: number;
  joinMode?: 0 | 1 | 2;
  tags?: number[];
  frozenTime?: number;
  withdrawRewardTime?: number;
  giftMoneyMode?: 1 | 2;
  giftMoneyCondition?: {
    min: number | null;
    max: number | null;
    feedback?: number;
    feedbackPercent?: number;
    feedbackUpperLimit?: number;
  }[];
  gradeSpan?: number; // 級距：1=單組，2以上=多組，且 groupCount = gradeSpan
  updatedBy: string;
  updatedAt: number;
};

export type DisActiveActivity = {
  id: number;
  templateType: number;
  contentMapping: {
    zhTw: {
      title: string;
      content: string;
      phone: string;
    };
  };
  conductType: number;
  conductWeek?: string;
  conductStartTime: number;
  conductEndTime: number;
  status: number;
  updatedBy: string;
  updatedAt: number;
};

// Unreviewed Active Orders interfaces
export interface UnreviewedActiveOrder {
  id: string;
  templateType: number;
  templateTypeLabel: string;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
      photo: string;
    };
  };
  account: string;
  tags: string[];
  reward: {
    type: string;
    rewardId: number;
    amount: number;
  }[];
  createdAt: number;
}

export interface UnreviewedActiveOrderSearchParams {
  templateType?: number;
  account?: string;
  page: number;
  limit: number;
}

// Activity Template interface for API response
export interface ActivityTemplate {
  id: number;
  label: string;
}
