import { useQuery } from '@tanstack/react-query';

import { getPropStatus } from '@/api/prop';

export const propStatusKeys = {
  all: ['propStatus'] as const,
  types: {
    all: ['propStatus', 'types'] as const,
    list: () => ['propStatus', 'types', 'list'] as const
  }
};

export const usePropStatus = () => {
  return useQuery({
    queryKey: propStatusKeys.types.list(),
    queryFn: () => getPropStatus(),
    select: (data) => data.data
  });
};
