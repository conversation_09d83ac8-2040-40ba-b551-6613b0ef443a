import { useQuery } from '@tanstack/react-query';

import { getPropList } from '@/api/prop';
import { PropListParams } from '@/types/props';

export const propListKeys = {
  all: ['propList'] as const,
  records: {
    all: ['propList', 'records'] as const,
    lists: () => ['propList', 'records', 'list'] as const,
    list: (params: PropListParams) => [...propListKeys.records.lists(), params] as const
  }
};

export const usePropList = (params: PropListParams) => {
  return useQuery({
    queryKey: propListKeys.records.list(params),
    queryFn: () => getPropList(params),
    select: (data) => data?.data
  });
};
