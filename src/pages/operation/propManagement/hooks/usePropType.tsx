import { useQuery } from '@tanstack/react-query';

import { getPropType } from '@/api/prop';

export const propTypeKeys = {
  all: ['propType'] as const,
  types: {
    all: ['propType', 'types'] as const,
    list: () => ['propType', 'types', 'list'] as const
  }
};

export const usePropType = () => {
  return useQuery({
    queryKey: propTypeKeys.types.list(),
    queryFn: () => getPropType(),
    select: (data) => data.data
  });
};
