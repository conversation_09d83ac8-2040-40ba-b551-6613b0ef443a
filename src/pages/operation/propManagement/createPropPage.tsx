import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Form, message, Upload } from 'antd';
import type { RcFile } from 'antd/es/upload/interface';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { createProp } from '@/api/prop';
import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RUploader from '@/components/RUploader';
import { useGiftSettingQuery } from '@/pages/transaction/giftSetting/hooks/useGiftSettingQuery';

import { propListKeys } from './hooks/usePropList';
import { usePropType } from './hooks/usePropType';

interface CreatePropPageProps {
  onBack: () => void;
  onSuccess: () => void;
}

interface CreatePropFormValues {
  type: number;
  zhTwTitle: string;
  zhTwContent: string;
  zhTwPhoto: File;
  note?: string;
  discountPercent: number;
  validityPeriodTimes: number;
}

export const CreatePropPage = ({ onBack, onSuccess }: CreatePropPageProps) => {
  const { t } = useTranslation();
  const [form] = Form.useForm<CreatePropFormValues>();
  const queryClient = useQueryClient();
  const [previewUrl, setPreviewUrl] = useState<string>('');

  const { data: propType } = usePropType();
  const { data: giftSetting } = useGiftSettingQuery();

  const { mutate: createPropMutation, isPending: isCreating } = useMutation({
    mutationFn: createProp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: propListKeys.all });
      form.resetFields();
      setPreviewUrl('');
      onSuccess();
    },
    onError: (error) => {
      console.error(error);
    }
  });

  const handleSubmit = (values: CreatePropFormValues) => {
    const { discountPercent, validityPeriodTimes, ...rest } = values;

    createPropMutation({
      ...rest,
      setting: {
        discountPercent,
        validityPeriodTimes
      }
    });
  };

  const currentHourSetting = giftSetting?.receiveGiftTimeLimit || 0;

  const handlePhotoChange = (file: File) => {
    // 創建預覽
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  };

  const propTypeOptions =
    propType?.map((type) => ({
      label: type.label,
      value: type.id
    })) || [];

  // 檔案上傳前的驗證
  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');

    // 檢查檔案大小
    if (isImage) {
      // 檢查圖片格式
      const allowedImageTypes = ['image/png', 'image/jpg', 'image/webp'];
      if (!allowedImageTypes.includes(file.type)) {
        message.error(t('components_uploader_invalid_image_type'));
        return Upload.LIST_IGNORE;
      }

      // 圖片檔案限制為 500KB
      const isLt500KB = file.size / 1024 < 500;
      if (!isLt500KB) {
        message.error(t('components_uploader_image_too_large', { size: 500 }));
        return Upload.LIST_IGNORE;
      }
    }

    return true;
  };

  return (
    <div className="p-6 bg-white">
      <RForm form={form} layout="vertical" onFinish={handleSubmit}>
        <div className="flex gap-4">
          <div className="w-1/2">
            <p className="text-sm font-bold mb-4 text-text-icon">{t('pages_role_baseSetting')}</p>
            <RForm.Item name="type" label={t('pages_operation_prop_type')}>
              <RSelect
                options={propTypeOptions}
                placeholder={t('placeholder_select')}
                className="!w-80 !h-8"
              />
            </RForm.Item>

            <RForm.Item
              name="zhTwTitle"
              label={t('pages_operation_prop_name')}
              rules={[{ required: true, message: t('common_required') }]}
            >
              <RInput placeholder={t('placeholder_input')} className="!w-80 !h-8" />
            </RForm.Item>

            <RForm.Item
              name="zhTwPhoto"
              label={t('pages_operation_prop_picture')}
              rules={[{ required: true, message: t('common_required') }]}
            >
              <RUploader
                accept="image/*"
                showPlaceholder={false}
                onChange={handlePhotoChange}
                note={t('pages_operation_prop_picture_description')}
                beforeUpload={beforeUpload}
              />
            </RForm.Item>

            {/* 圖片預覽 */}
            {previewUrl && (
              <div className="my-4">
                <p className="text-xs text-gray-500 mb-2">{t('pages_carousel_previewImage')}</p>
                <div className="w-25 h-25 border border-gray-200 rounded overflow-hidden">
                  <img src={previewUrl} alt="preview" className="w-full h-full object-cover" />
                </div>
              </div>
            )}

            <RForm.Item name="zhTwContent" label={t('pages_operation_prop_content')}>
              <RInput.TextArea
                rows={4}
                placeholder={t('pages_operation_prop_content_placeholder')}
                className="!w-80 "
              />
            </RForm.Item>

            <RForm.Item name="note" label={t('pages_transaction_balanceAdjustment_note')}>
              <RInput placeholder={t('placeholder_input')} className="!w-80 !h-8" />
            </RForm.Item>
          </div>

          <div className="w-1/2 relative before:content-[''] before:absolute before:top-0 before:-left-5 before:w-[1px] before:h-full before:bg-gray-200">
            <p className="text-sm font-bold mb-4 text-text-icon">
              {t('pages_operation_prop_settings')}
            </p>
            <RForm.Item
              name="discountPercent"
              label={t('pages_operation_prop_discountPercent')}
              rules={[
                { required: true, message: t('common_required') },
                {
                  validator: (_, value) => {
                    if (value !== undefined && value !== null && value < 0) {
                      return Promise.reject(new Error(t('common_min_value_error')));
                    }
                    if (value > 100) {
                      return Promise.reject(new Error(t('common_max_value_error')));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <div className="flex items-center">
                <RInput
                  type="number"
                  placeholder={t('placeholder_input')}
                  className="!w-40 !h-8"
                  min={0}
                  max={100}
                />
                <span className="ml-2 text-gray-500">%</span>
              </div>
            </RForm.Item>

            <RForm.Item
              name="validityPeriodTimes"
              label={t('pages_operation_prop_validityPeriodTimes')}
              rules={[
                { required: true, message: t('common_required') },
                {
                  validator: (_, value) => {
                    if (value > 0 && value <= currentHourSetting) {
                      return Promise.reject(
                        new Error(t('pages_operation_prop_validityPeriodTimes_error'))
                      );
                    }
                    if (value < 0) {
                      return Promise.reject(new Error(t('common_min_value_error')));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <div className="flex items-center">
                <RInput
                  type="number"
                  placeholder={t('placeholder_input')}
                  className="!w-40 !h-8"
                  min={0}
                />
                <span className="ml-2 text-gray-500">{t('common_hour')}</span>
              </div>
            </RForm.Item>

            {/* 當前設定顯示 - 純展示欄位 */}
            <div className="mb-4">
              <label className="block text-text-placeholder mb-1">
                {t('pages_operation_prop_currentSetting')}
              </label>
              <div className="flex items-center">
                <RInput
                  value={currentHourSetting}
                  disabled={true}
                  className="!w-40 !h-8"
                  readOnly
                />
                <span className="ml-2">{t('common_hour')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 操作按鈕 */}
        <div className="flex justify-center gap-4 mt-8">
          <RButton type="primary" htmlType="submit" loading={isCreating} className="!w-32">
            {t('common_save')}
          </RButton>
          <RButton type="primary" variant="outlined" onClick={onBack} className="!w-32">
            {t('common_cancel')}
          </RButton>
        </div>
      </RForm>
    </div>
  );
};
