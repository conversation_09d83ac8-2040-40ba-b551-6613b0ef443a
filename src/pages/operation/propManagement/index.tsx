import { useMutation, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { editBackpackSetting, editPropStatus } from '@/api/prop';
import ArrowLeft from '@/assets/img/icon/arrow-left.svg?react';
import OperatorCell from '@/components/cells/OperatorCell';
import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import SearchForm from '@/components/SearchForm';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import useToggleElementVisibility from '@/hooks/useToggleElementVisibility';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Prop, PropListParams, PropStatus } from '@/types/props';

import { BackpackSettingModal } from './backpackSettingModal';
import { CreatePropPage } from './createPropPage';
import { usePropList } from './hooks/usePropList';
import { propListKeys } from './hooks/usePropList';
import { usePropStatus } from './hooks/usePropStatus';
import { usePropType } from './hooks/usePropType';

type SearchFormValues = Omit<PropListParams, 'page' | 'limit'>;

// 定義頁面狀態枚舉
enum PropPageView {
  TABLE = 'table',
  CREATE = 'create'
}

const ActionButtons = ({ data, onDisable }: { data: Prop; onDisable: (data: Prop) => void }) => {
  const { t } = useTranslation();

  const handleDisable = () => {
    onDisable(data);
  };

  return (
    <div className="flex gap-1">
      {data.status === PropStatus.ENABLE && (
        <RButton size="small" variant="outlined" color="red" type="link" onClick={handleDisable}>
          {t('common_disable')}
        </RButton>
      )}
      {data.status === PropStatus.DISABLE && (
        <RButton size="small" variant="outlined" color="green" type="link" onClick={handleDisable}>
          {t('common_enable')}
        </RButton>
      )}
    </div>
  );
};

const getTableColumns = (
  t: (key: string) => string,
  propType: { id: number; label: string }[],
  propStatus: { id: number; label: string }[],
  onDisable: (data: Prop) => void
) => [
  {
    title: t('pages_operation_prop_key'),
    dataIndex: 'key'
  },
  {
    title: t('pages_operation_prop_type'),
    dataIndex: 'type',
    render: (_: any, record: Prop) => propType?.find((item) => item.id === record.type)?.label
  },
  {
    title: t('pages_operation_prop_name'),
    dataIndex: 'contentMapping.zhTw.title',
    render: (_: any, record: Prop) => record?.contentMapping?.zhTw?.title
  },
  {
    title: t('pages_operation_prop_icon'),
    dataIndex: 'contentMapping.zhTw.photo',
    render: (_: any, record: Prop) => (
      <img
        src={record?.contentMapping?.zhTw?.photo || ''}
        alt="prop icon"
        style={{ width: 50, height: 50 }}
      />
    )
  },
  {
    title: t('pages_operation_prop_createdBy'),
    dataIndex: 'createdBy',
    render: (_: any, record: Prop) => (
      <span>
        {record.createdBy} /
        <br />
        {dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')}
      </span>
    )
  },
  {
    title: t('pages_operation_prop_status'),
    dataIndex: 'status',
    render: (_: any, record: Prop) => {
      const status = propStatus?.find((item) => item.id === record.status);
      const color =
        record.status === PropStatus.DISABLE
          ? 'red'
          : record.status === PropStatus.ENABLE
            ? 'green'
            : 'gray';
      return <RTag color={color}>{status?.label}</RTag>;
    }
  },
  {
    title: t('common_operator'),
    dataIndex: 'operator',
    render: (_: any, record: Prop) => {
      if (record.status === PropStatus.ENABLE || record.status === PropStatus.DISABLE) {
        return <ActionButtons data={record} onDisable={onDisable} />;
      }
      return null;
    }
  },
  {
    title: t('common_lastOperate'),
    dataIndex: 'operatorAndTime',
    render: (_: any, record: Prop) => (
      <OperatorCell record={{ updatedBy: record.updatedBy, updatedAt: record.updatedAt }} />
    )
  },
  {
    title: t('pages_transaction_balanceAdjustment_note'),
    dataIndex: 'note'
  }
];

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: Omit<PropListParams, 'page' | 'limit'>) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();
  const { data: propType } = usePropType();
  const { data: propStatus } = usePropStatus();
  const propTypeOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    const apiOptions =
      propType?.map((type) => ({
        label: type.label,
        value: type.id
      })) || [];
    return [allOption, ...apiOptions];
  }, [propType, t]);
  const propStatusOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    const apiOptions =
      propStatus?.map((status) => ({
        label: status.label,
        value: status.id
      })) || [];
    return [allOption, ...apiOptions];
  }, [propStatus, t]);

  const handleSearch = (values: SearchFormValues) => {
    const processedValues = Object.entries(values).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: value === 'all' ? undefined : value
      }),
      {}
    );
    onSearch(processedValues);
  };
  return (
    <SearchForm<SearchFormValues>
      onSearch={handleSearch}
      onReset={onReset}
      className=""
      initialValues={{
        type: 'all' as unknown as number,
        status: 'all' as unknown as number
      }}
    >
      <RForm.Item name="key" label={t('pages_operation_prop_key')}>
        <RInput />
      </RForm.Item>
      <RForm.Item name="type" label={t('pages_operation_prop_type')}>
        <RSelect options={propTypeOptions} />
      </RForm.Item>
      <RForm.Item name="status" label={t('pages_operation_prop_status')}>
        <RSelect options={propStatusOptions} />
      </RForm.Item>
      <RForm.Item name="name" label={t('pages_operation_prop_name')}>
        <RInput />
      </RForm.Item>
    </SearchForm>
  );
};

export default function PropManagement() {
  const { confirmModal } = useConfirmModal();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<Omit<PropListParams, 'page' | 'limit'>>({});
  const [currentView, setCurrentView] = useState<PropPageView>(PropPageView.TABLE);
  const { data: propList, isLoading: isPropListLoading } = usePropList({
    ...params,
    page: 1,
    limit: 10
  });
  const { data: propType } = usePropType();
  const { data: propStatus } = usePropStatus();

  useToggleElementVisibility('base-layout-header', currentView === PropPageView.TABLE);

  const { mutate: mutateBackpackSetting, isPending: isSettingLoading } = useMutation({
    mutationFn: editBackpackSetting,
    onSuccess: () => {
      setIsSettingModalOpen(false);
    },
    onError: (error) => {
      console.error(error);
    }
  });

  const { mutate: mutatePropStatus } = useMutation({
    mutationFn: editPropStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: propListKeys.all });
    },
    onError: (error: any) => {
      // 檢查是否為 400003 錯誤 ： 道具已與活動綁定，故無法停用，若需停用，請先修改活動條件設計-贈送道具
      if (error?.code === 400003) {
        const activityList = Array.isArray(error.data) ? error.data : [];

        confirmModal({
          title: t('common_alert'),
          content: (
            <div className="w-68 mx-auto">
              {t('pages_operation_prop_status_change_error_400003')}
              <br />
              <div className="text-sm text-warning mt-2">
                {t('pages_operation_prop_bindActivity')}：
                <ul className="list-disc list-inside mt-1">
                  {activityList.map((activity: any, index: number) => (
                    <li key={index}>{activity?.zhTw?.title}</li>
                  ))}
                </ul>
              </div>
            </div>
          ),
          onOk: () => {
            navigate('/operation/activity');
          },
          okText: t('pages_operation_prop_goToActivity')
        });
      }
    }
  });

  const handleSearch = (values: SearchFormValues) => {
    setParams(values);
    setPage(1);
  };
  const handleReset = () => {
    setParams({});
    setPage(1);
  };
  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const [isSettingModalOpen, setIsSettingModalOpen] = useState(false);

  const handleSettingSubmit = (values: {
    singleExpandPoints: number;
    backpackExpandLimit: number;
  }) => {
    mutateBackpackSetting(values);
  };

  const handleAdd = () => {
    setCurrentView(PropPageView.CREATE);
  };

  const handleBackToTable = () => {
    setCurrentView(PropPageView.TABLE);
  };

  const handleFormSuccess = () => {
    handleBackToTable();
  };

  const handleDisable = useCallback(
    (data: Prop) => {
      const newStatus = data.status === 1 ? 0 : 1;
      // 確認是否要修改道具狀態 注意事項
      if (data.status === PropStatus.ENABLE) {
        confirmModal({
          title: t('pages_player_vip_notice'),
          content: <div className="w-84 mx-auto">{t('pages_operation_prop_disableInfo')}</div>,
          onOk: () => {
            mutatePropStatus({ id: data.id, status: newStatus });
          }
        });
      } else {
        confirmModal({
          title: t('pages_player_vip_notice'),
          content: <div className="w-84 mx-auto">{t('pages_operation_prop_enableInfo')}</div>,
          onOk: () => {
            mutatePropStatus({ id: data.id, status: newStatus });
          }
        });
      }
    },
    [t, confirmModal, mutatePropStatus]
  );

  const tableColumns = useMemo(
    () => getTableColumns(t, propType || [], propStatus || [], handleDisable),
    [t, propType, propStatus, handleDisable]
  );

  // 渲染創建頁面
  if (currentView === PropPageView.CREATE) {
    return (
      <div>
        <div className="flex gap-2 justify-start items-center px-4 py-4.5 w-full bg-white shadow-content z-1">
          <ArrowLeft
            className="cursor-pointer fill-gray-400 h-[20px]"
            onClick={handleBackToTable}
          />
          <h2 className="m-0 text-sm font-bold">{t('pages_operation_prop_add')}</h2>
        </div>
        <TableSearchLayout>
          <CreatePropPage onBack={handleBackToTable} onSuccess={handleFormSuccess} />
        </TableSearchLayout>
      </div>
    );
  }

  // 渲染表格頁面
  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
    >
      <div>
        <RButton type="primary" onClick={handleAdd} className="!w-22 mb-4">
          {t('pages_operation_prop_add')}
        </RButton>
        <RButton
          type="primary"
          onClick={() => setIsSettingModalOpen(true)}
          className="!w-28 mb-4 ml-4"
        >
          {t('pages_operation_prop_setting')}
        </RButton>
      </div>

      <RTable
        loading={isPropListLoading}
        rowKey="id"
        dataSource={propList?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: propList?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />

      <BackpackSettingModal
        open={isSettingModalOpen}
        onClose={() => setIsSettingModalOpen(false)}
        onSubmit={handleSettingSubmit}
        isLoading={isSettingLoading}
      />
    </TableSearchLayout>
  );
}
