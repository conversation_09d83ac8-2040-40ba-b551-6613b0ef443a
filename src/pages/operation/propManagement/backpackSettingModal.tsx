import { useTranslation } from 'react-i18next';

import FormModal from '@/components/FormModal';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';

type FormValue = {
  singleExpandPoints: number;
  backpackExpandLimit: number;
};

export const BackpackSettingModal = ({
  open,
  onClose,
  onSubmit,
  initialValues,
  isLoading = false
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormValue) => void;
  initialValues?: FormValue;
  isLoading?: boolean;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();

  const handleSubmit = (values: FormValue) => {
    onSubmit(values);
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  return (
    <FormModal
      title={t('pages_operation_prop_setting')}
      open={open}
      onClose={handleClose}
      width={360}
      form={form}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      initialValues={{
        singleExpandPoints: initialValues?.singleExpandPoints || 10000,
        backpackExpandLimit: initialValues?.backpackExpandLimit || 0
      }}
    >
      <div>
        <RForm.Item
          name="singleExpandPoints"
          label={t('pages_operation_prop_singleExpandPoints')}
          rules={[
            {
              validator: (_, value) => {
                if (value !== undefined && value !== null && value < 0) {
                  return Promise.reject(new Error(t('common_min_value_error')));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <RInput type="number" className="!w-40 !h-8" min={0} />
        </RForm.Item>
        <RForm.Item
          name="backpackExpandLimit"
          label={t('pages_operation_prop_backpackExpandLimit')}
          rules={[
            {
              validator: (_, value) => {
                if (value !== undefined && value !== null && value < 0) {
                  return Promise.reject(new Error(t('common_min_value_error')));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <RInput type="number" className="!w-40 !h-8" min={0} />
        </RForm.Item>
      </div>
    </FormModal>
  );
};
