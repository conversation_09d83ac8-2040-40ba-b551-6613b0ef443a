import { useQuery } from '@tanstack/react-query';

import { getGiftMailVariables } from '@/api/reward';

// Query keys for gift mail variables
export const giftMailVariablesKeys = {
  all: ['giftMailVariables'] as const,
  lists: () => [...giftMailVariablesKeys.all, 'list'] as const
};

// Hook to get gift mail variables
export const useGiftMailVariables = () => {
  return useQuery({
    queryKey: giftMailVariablesKeys.lists(),
    queryFn: () => getGiftMailVariables(),
    select: (data) => data.data
  });
};
