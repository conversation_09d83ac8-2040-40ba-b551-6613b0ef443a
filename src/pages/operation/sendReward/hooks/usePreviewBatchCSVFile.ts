import { useQuery } from '@tanstack/react-query';

import { getPreviewBatchCSVFile } from '@/api/reward';

// Query keys for gift mail variables
export const previewBatchCSVFileKeys = {
  all: ['previewBatchCSVFile'] as const,
  lists: () => [...previewBatchCSVFileKeys.all, 'list'] as const,
  list: (params: { file?: File; inValid?: boolean }) =>
    [...previewBatchCSVFileKeys.lists(), params] as const
};

// Hook to get gift mail variables
export const usePreviewBatchCSVFile = ({ file, inValid }: { file?: File; inValid?: boolean }) => {
  return useQuery({
    queryKey: previewBatchCSVFileKeys.list({ file, inValid }),
    queryFn: () => {
      if (!file) {
        throw new Error('File is required');
      }
      return getPreviewBatchCSVFile({ file, inValid });
    },
    select: (data) => data.data,
    enabled: !!file
  });
};
