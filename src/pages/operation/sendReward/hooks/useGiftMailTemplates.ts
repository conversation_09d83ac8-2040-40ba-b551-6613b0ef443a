import { useQuery } from '@tanstack/react-query';

import { getGiftMailTemplates } from '@/api/reward';

// Query keys for gift mail templates
export const giftMailTemplatesKeys = {
  all: ['giftMailTemplates'] as const,
  lists: () => [...giftMailTemplatesKeys.all, 'list'] as const
};

// Hook to get gift mail templates
export const useGiftMailTemplates = () => {
  return useQuery({
    queryKey: giftMailTemplatesKeys.lists(),
    queryFn: () => getGiftMailTemplates(),
    select: (data) => data.data
  });
};
