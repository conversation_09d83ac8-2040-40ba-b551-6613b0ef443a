import { useQuery } from '@tanstack/react-query';

import { getGiftBatchSampleCsvFileLink } from '@/api/reward';

// Query keys for gift batch sample CSV file link
export const giftBatchSampleCsvFileLinkKeys = {
  all: ['giftBatchSampleCsvFileLink'] as const,
  links: () => [...giftBatchSampleCsvFileLinkKeys.all, 'link'] as const
};

// Hook to get gift batch sample CSV file link
export const useGiftBatchSampleCsvFileLink = () => {
  return useQuery({
    queryKey: giftBatchSampleCsvFileLinkKeys.links(),
    queryFn: () => getGiftBatchSampleCsvFileLink(),
    select: (data) => data.data
  });
};
