import { useQuery } from '@tanstack/react-query';

import { getRewardTypes } from '@/api/reward';

// Query keys for reward types
export const rewardTypesKeys = {
  all: ['rewardTypes'] as const,
  lists: () => [...rewardTypesKeys.all, 'list'] as const
};

// Hook to get reward types
export const useRewardTypes = () => {
  return useQuery({
    queryKey: rewardTypesKeys.lists(),
    queryFn: () => getRewardTypes(),
    select: (data) => data.data
  });
};
