import { useQuery } from '@tanstack/react-query';

import { getRewardProp } from '@/api/reward';

// Query keys for reward prop
export const rewardPropKeys = {
  all: ['rewardProp'] as const,
  lists: () => [...rewardPropKeys.all, 'list'] as const
};

// Hook to get reward props
export const useRewardProp = () => {
  return useQuery({
    queryKey: rewardPropKeys.lists(),
    queryFn: () => getRewardProp(),
    select: (data) => data.data
  });
};
