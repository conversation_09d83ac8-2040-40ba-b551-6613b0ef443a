import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import RCheckbox from '@/components/RCheckbox';
import RModal from '@/components/RModal';
import RTable from '@/components/RTable';

import { usePreviewBatchCSVFile } from './hooks';

type BatchGiftGivingPreviewModalProps = {
  open: boolean;
  onClose: () => void;
  onOk: () => void;
  csvFile?: File;
};

const BatchGiftGivingPreviewModal = ({
  open,
  onClose,
  onOk,
  csvFile
}: BatchGiftGivingPreviewModalProps) => {
  const { t } = useTranslation();
  const [onlyShowValidRecord, setOnlyShowValidRecord] = useState(false);
  const previewBatchCSVFileQuery = usePreviewBatchCSVFile({
    file: csvFile,
    inValid: onlyShowValidRecord
  });

  const previewList = useMemo(() => {
    return (previewBatchCSVFileQuery.data?.preview || []).map((item, index) => ({
      ...item,
      key: index
    }));
  }, [previewBatchCSVFileQuery.data]);

  console.log('previewBatchCSVFileQuery', previewBatchCSVFileQuery.data);

  const column = useMemo(
    () => [
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_isValid'),
        dataIndex: 'isValid',
        key: 'isValid',
        render: (isValid: boolean) => (isValid ? t('common_yes') : t('common_no'))
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_activityName'),
        dataIndex: 'activityName',
        key: 'activityName'
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_playerAccount'),
        dataIndex: 'playerAccount',
        key: 'playerAccount'
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_rewardType'),
        dataIndex: 'rewardType',
        key: 'rewardType'
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_rewardItemId'),
        dataIndex: 'rewardItemId',
        key: 'rewardItemId'
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_rewardValue'),
        dataIndex: 'rewardValue',
        key: 'rewardValue'
      },
      {
        title: t('pages_operation_rewardList_batchGiftGivingPreviewModal_frozenTime'),
        dataIndex: 'frozenTime',
        key: 'frozenTime'
      }
    ],
    [t]
  );

  return (
    <RModal
      centered
      maskClosable={false}
      title={t('pages_operation_rewardList_batchGiftGivingPreviewModal_title')}
      open={open}
      onCancel={onClose}
      onOk={onOk}
      loading={previewBatchCSVFileQuery.isFetching}
      width={700}
      destroyOnClose={true}
      forceRender={true}
      okButtonProps={{ show: true }}
      cancelButtonProps={{ show: false }}
    >
      <div className="flex flex-col gap-4">
        <div className="text-sm text-gray-700">
          {t('pages_operation_rewardList_batchGiftGivingPreviewModal_totalDataSummary', {
            total: previewBatchCSVFileQuery.data?.summary.total || 0,
            valid: previewBatchCSVFileQuery.data?.summary.valid || 0
          })}
        </div>
        <RCheckbox
          checked={onlyShowValidRecord}
          onChange={(e) => setOnlyShowValidRecord(e.target.checked)}
        >
          {t('pages_operation_rewardList_batchGiftGivingPreviewModal_showOnlyInvalidData')}
        </RCheckbox>
        <RTable
          rowKey="key"
          dataSource={previewList}
          columns={column}
          pagination={false}
          scroll={{ x: 800, y: 400 }}
          size="small"
        />
      </div>
    </RModal>
  );
};

export default BatchGiftGivingPreviewModal;
