import { useMutation } from '@tanstack/react-query';
import { Col, Radio, Row } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { confirmBatchCSVFile, createGiftGiving } from '@/api/reward';
import HTMLEditor from '@/components/HTMLEditor';
import RButton from '@/components/RButton';
import RCheckbox from '@/components/RCheckbox';
import RDatePicker from '@/components/RDatePicker';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RUploader from '@/components/RUploader';
import {
  CollectionMethod,
  ConfirmBatchCSVFormValue,
  GiftGivingFormValue,
  RewardType,
  SendingMethod,
  SendSiteMessageNotificationType,
  TimeLimitInputMethod
} from '@/types/reward';

import BatchGiftGivingPreviewModal from './BatchGiftGivingPreviewModal';
import {
  useGiftBatchSampleCsvFileLink,
  useGiftMailTemplates,
  useGiftMailVariables,
  usePreviewBatchCSVFile,
  useRewardProp
} from './hooks';

// Internal form state type for rendering conditions
type InternalFormState = {
  sendingMethod: SendingMethod;
  account?: string;
  activityName?: string;
  rewardType?: RewardType;
  quantity?: number;
  freezeTime?: number;
  tool?: number;
  collectionMethod?: CollectionMethod;
  remark?: string;
  timeLimitInputMethod?: TimeLimitInputMethod;
  hours?: number;
  specificTime?: string;
  arrivalTime?: number;
  sendSiteMessageNotification?: boolean;
  sendSiteMessageNotificationType?: SendSiteMessageNotificationType;
  existingTemplate?: number;
  templateTitle?: string;
  templateContent?: string;
  templateRemark?: string;
  customTitle?: string;
  customContent?: string;
};

const activeLanguage = 'zhTw';

const GiftGiving = () => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<InternalFormState>();
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const onCreateSuccess = () => {
    form.resetFields();
  };

  const createSingleGiftMutation = useMutation({
    mutationFn: createGiftGiving,
    onSuccess: onCreateSuccess
  });
  const createBatchGiftMutation = useMutation({
    mutationFn: confirmBatchCSVFile,
    onSuccess: onCreateSuccess
  });
  const sampleCsvFileLinkQuery = useGiftBatchSampleCsvFileLink();
  const giftMailTemplateQuery = useGiftMailTemplates();
  const giftMailVariablesQuery = useGiftMailVariables();
  const rewardPropQuery = useRewardProp();
  const allGiftMailVariables = useMemo(() => {
    return giftMailVariablesQuery.data?.flatMap((variable) => variable.variables);
  }, [giftMailVariablesQuery.data]);

  // Create refs for HTMLEditor instances - one per language
  const editorRefs = useRef<{ [key: string]: any }>({});

  const sendingMethod = RForm.useWatch<SendingMethod>('sendingMethod', form);
  const rewardType = RForm.useWatch('rewardType', form);
  const collectionMethod = RForm.useWatch('collectionMethod', form);
  const timeLimitInputMethod = RForm.useWatch('timeLimitInputMethod', form);
  const sendSiteMessageNotification = RForm.useWatch('sendSiteMessageNotification', form);
  const sendSiteMessageNotificationType = RForm.useWatch('sendSiteMessageNotificationType', form);
  const existingTemplate = RForm.useWatch<number>('existingTemplate', form);
  const csvFile = RForm.useWatch('csvFile', form);

  const previewBatchCSVFileQuery = usePreviewBatchCSVFile({
    file: csvFile
  });

  const isLoading = useMemo(
    () =>
      createSingleGiftMutation.isPending ||
      createBatchGiftMutation.isPending ||
      previewBatchCSVFileQuery.isFetching,
    [
      createBatchGiftMutation.isPending,
      createSingleGiftMutation.isPending,
      previewBatchCSVFileQuery.isFetching
    ]
  );

  useEffect(() => {
    if (existingTemplate) {
      const selectedTemplate = giftMailTemplateQuery.data?.find(
        (template) => template.id === existingTemplate
      );
      const timeout = setTimeout(() => {
        form.setFieldsValue({
          templateTitle: selectedTemplate?.contentMapping[activeLanguage]?.title,
          templateContent: selectedTemplate?.contentMapping[activeLanguage]?.content
        });
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [existingTemplate, form, giftMailTemplateQuery.data]);

  // Handle variable click to insert into HTMLEditor
  const handleVariableClick = (variableValue: string) => {
    const formattedVariable = `{{${variableValue}}}`;

    // Get the current active language editor
    const currentEditor = editorRefs.current[activeLanguage];

    if (currentEditor) {
      // Insert the formatted variable at the current cursor position
      currentEditor.insertContent(formattedVariable);
      // Focus the editor after insertion
      currentEditor.focus();
    }
  };

  const handlePreviewCSVFile = async () => {
    setIsPreviewModalOpen(true);
  };

  // Transform internal form state to API structure
  const transformToSingleCreateApiFormat = (values: InternalFormState): GiftGivingFormValue => {
    const rewardType = values.rewardType === RewardType.MAIN_POINTS ? 1 : 2; // 1 for points, 2 for tool
    return {
      account: values.account!,
      reward: [
        {
          type: rewardType,
          rewardId: rewardType === RewardType.PROP ? values.tool! : 0,
          amount: rewardType === RewardType.MAIN_POINTS ? values.quantity! : 1
        }
      ],
      contentMapping: {
        [activeLanguage]: { title: values.activityName! }
      },
      templateId:
        sendSiteMessageNotificationType === SendSiteMessageNotificationType.USE_EXISTING_TEMPLATE
          ? values.existingTemplate!
          : undefined,
      method: values.collectionMethod!,
      frozenTime: rewardType === RewardType.MAIN_POINTS ? values.freezeTime! : 0,
      expiredType: values.timeLimitInputMethod!,
      expiredTime:
        values.timeLimitInputMethod === TimeLimitInputMethod.ENTER_HOUR
          ? values.hours!
          : values.specificTime?.valueOf(),
      sendMail: values.sendSiteMessageNotification ? 1 : 0,
      mailContentMapping:
        sendSiteMessageNotificationType === SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
          ? {
              [activeLanguage]: {
                title:
                  sendSiteMessageNotificationType ===
                  SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
                    ? values.customTitle!
                    : values.templateTitle!,
                content:
                  sendSiteMessageNotificationType ===
                  SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
                    ? values.customContent!
                    : values.templateContent!
              }
            }
          : undefined,
      note: {
        [activeLanguage]: values.remark!
      },
      frontendNote: {
        [activeLanguage]: values.templateRemark!
      }
    };
  };

  const transformToBatchCreateApiFormat = (values: InternalFormState): ConfirmBatchCSVFormValue => {
    return {
      templateId:
        sendSiteMessageNotificationType === SendSiteMessageNotificationType.USE_EXISTING_TEMPLATE
          ? values.existingTemplate!
          : undefined,
      method: values.collectionMethod!,
      expiredType: values.timeLimitInputMethod!,
      expiredTime:
        values.timeLimitInputMethod === TimeLimitInputMethod.ENTER_HOUR
          ? values.hours!
          : values.specificTime?.valueOf(),
      sendMail: values.sendSiteMessageNotification ? 1 : 0,
      mailContentMapping:
        sendSiteMessageNotificationType === SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
          ? {
              [activeLanguage]: {
                title:
                  sendSiteMessageNotificationType ===
                  SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
                    ? values.customTitle!
                    : values.templateTitle!,
                content:
                  sendSiteMessageNotificationType ===
                  SendSiteMessageNotificationType.CUSTOMIZE_CONTENT
                    ? values.customContent!
                    : values.templateContent!
              }
            }
          : undefined,
      note: {
        [activeLanguage]: values.remark!
      },
      frontendNote: {
        [activeLanguage]: values.templateRemark!
      },
      rows:
        previewBatchCSVFileQuery.data?.preview?.map((row) => ({
          activityName: row.activityName,
          playerAccount: row.playerAccount,
          rewardType: row.rewardType,
          rewardItemId: row.rewardItemId,
          rewardValue: parseInt(row.rewardValue),
          frozenTime: row.frozenTime
        })) || []
    };
  };

  const handleSubmit = (values: InternalFormState) => {
    try {
      if (values.sendingMethod === SendingMethod.SINGLE_SENDING) {
        const apiPayload = transformToSingleCreateApiFormat(values);
        createSingleGiftMutation.mutate(apiPayload);
      } else if (values.sendingMethod === SendingMethod.BATCH_SENDING) {
        const apiPayload = transformToBatchCreateApiFormat(values);
        createBatchGiftMutation.mutate(apiPayload);
      }
    } catch (err) {
      console.error('Failed to transform form values:', err);
    }
  };

  return (
    <div className="p-3">
      <RForm<InternalFormState> form={form} onFinish={handleSubmit} layout="vertical">
        {/* 選擇發送方式 */}
        <div className="bg-white rounded p-4 mb-4">
          <h3 className="text-base font-medium mb-3 flex items-center">
            {t('pages_operation_rewardList_giftGiving_selectSendingMethod')}
          </h3>
          <RForm.Item
            name="sendingMethod"
            className="mb-0"
            initialValue={SendingMethod.SINGLE_SENDING}
          >
            <Radio.Group>
              <Radio value={SendingMethod.SINGLE_SENDING}>
                {t('pages_operation_rewardList_giftGiving_singleSending')}
              </Radio>
              <Radio value={SendingMethod.BATCH_SENDING}>
                {t('pages_operation_rewardList_giftGiving_batchSending')}
              </Radio>
            </Radio.Group>
          </RForm.Item>
        </div>

        {/* 對象設定 */}
        <div className="bg-white rounded p-4 mb-4">
          <h3 className="text-base font-medium mb-3">
            {t('pages_operation_rewardList_giftGiving_giftItemSetting')}
          </h3>
          {sendingMethod === SendingMethod.SINGLE_SENDING && (
            <Row gutter={16}>
              <Col span={12}>
                <RForm.Item
                  label={t('pages_operation_rewardList_giftGiving_enterPlayerAccount')}
                  name="account"
                  rules={[{ required: true }]}
                >
                  <RInput
                    className="max-h-[32px]"
                    placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                  />
                </RForm.Item>
                <h3 className="text-base font-medium mb-3">
                  {t('pages_operation_rewardList_giftGiving_giftItemSetting')}
                </h3>
                <RForm.Item
                  label={t('pages_operation_rewardList_giftGiving_enterActivityName')}
                  name="activityName"
                  rules={[{ required: true }]}
                >
                  <RInput
                    className="max-h-[32px]"
                    placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                  />
                </RForm.Item>
                <RForm.Item
                  label={t('pages_operation_rewardList_giftGiving_rewardType')}
                  name="rewardType"
                  initialValue={RewardType.MAIN_POINTS}
                >
                  <RSelect
                    options={[
                      {
                        label: t('pages_operation_rewardList_giftGiving_mainPoints'),
                        value: RewardType.MAIN_POINTS
                      },
                      {
                        label: t('pages_operation_rewardList_giftGiving_tool'),
                        value: RewardType.PROP
                      }
                    ]}
                    placeholder={t('pages_operation_rewardList_giftGiving_mainPoints')}
                  />
                </RForm.Item>
                {rewardType === RewardType.MAIN_POINTS && (
                  <>
                    <RForm.Item
                      label={t('pages_operation_rewardList_giftGiving_quantity')}
                      name="quantity"
                      rules={[{ required: true, min: 0 }]}
                    >
                      <RInput
                        className="max-h-[32px]"
                        placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                        type="number"
                        min={0}
                      />
                    </RForm.Item>
                    <RForm.Item
                      label={t('pages_operation_rewardList_giftGiving_freezeTime')}
                      name="freezeTime"
                      rules={[{ required: true, min: 0 }]}
                    >
                      <RInput
                        className="max-h-[32px]"
                        placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                        type="number"
                        min={0}
                      />
                    </RForm.Item>
                  </>
                )}
                {rewardType === RewardType.PROP && (
                  <>
                    <RForm.Item label={t('pages_operation_rewardList_giftGiving_tool')} name="tool">
                      <RSelect
                        options={rewardPropQuery.data?.map((item) => ({
                          label: item.contentMapping[activeLanguage]?.title || item.id.toString(),
                          value: item.id
                        }))}
                        loading={rewardPropQuery.isLoading}
                        placeholder={t('pages_operation_rewardList_giftGiving_pleaseSelect')}
                      />
                    </RForm.Item>
                    <p>{t('pages_operation_rewardList_giftGiving_toolType')}</p>
                  </>
                )}
              </Col>
            </Row>
          )}
          {sendingMethod === SendingMethod.BATCH_SENDING && (
            <Row>
              <Col span={16}>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm text-blue-800">
                      {t('pages_operation_rewardList_giftGiving_batchUploadInstructions')}
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-900">
                        {t('pages_operation_rewardList_giftGiving_downloadSampleFile')}
                      </span>
                    </div>
                    <RButton
                      type="primary"
                      onClick={() => {
                        if (sampleCsvFileLinkQuery.data) {
                          window.open(sampleCsvFileLinkQuery.data, '_blank');
                        }
                      }}
                      loading={sampleCsvFileLinkQuery.isLoading}
                    >
                      {t('pages_operation_rewardList_giftGiving_clickToDownload')}
                    </RButton>
                  </div>

                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex flex-col space-y-3">
                      <span className="font-medium text-gray-900">
                        {t('pages_operation_rewardList_giftGiving_uploadSampleFile')}
                      </span>
                      <div className="flex items-center space-x-3">
                        <div className="flex-1">
                          <RForm.Item name="csvFile">
                            <RUploader accept="text/csv" />
                          </RForm.Item>
                        </div>
                        <RButton
                          type="primary"
                          className="mb-[24px]"
                          onClick={handlePreviewCSVFile}
                          disabled={!csvFile}
                        >
                          {t('pages_operation_rewardList_giftGiving_preview')}
                        </RButton>
                        {(previewBatchCSVFileQuery.data?.summary?.invalid || 0) > 0 && (
                          <span className="text-red-500 mb-[24px]">
                            {t('pages_operation_rewardList_giftGiving_invalidDataFound', {
                              count: previewBatchCSVFileQuery.data?.summary?.invalid || 0
                            })}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          )}
        </div>

        <div className="bg-white rounded p-4 mb-4">
          <h3 className="text-base font-medium mb-3">
            {t('pages_operation_rewardList_giftGiving_collectionMethodSetting')}
          </h3>
          <Row gutter={16}>
            <Col span={16}>
              <RForm.Item
                name="collectionMethod"
                className="mb-0"
                initialValue={CollectionMethod.REWARD_CENTER}
              >
                <Radio.Group>
                  <Radio value={CollectionMethod.REWARD_CENTER}>
                    {t('pages_operation_rewardList_giftGiving_rewardCenter')}
                  </Radio>
                  <Radio value={CollectionMethod.INSTANT_ARRIVAL}>
                    {t('pages_operation_rewardList_giftGiving_instantArrival')}
                  </Radio>
                </Radio.Group>
              </RForm.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <RForm.Item
                label={t('pages_operation_rewardList_giftGiving_backendRemark')}
                name="remark"
              >
                <RInput
                  className="max-h-[32px]"
                  placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                />
              </RForm.Item>
              {collectionMethod === CollectionMethod.REWARD_CENTER && (
                <>
                  <RForm.Item
                    name="timeLimitInputMethod"
                    className="mb-0"
                    initialValue={TimeLimitInputMethod.ENTER_HOUR}
                  >
                    <Radio.Group>
                      <Radio value={TimeLimitInputMethod.ENTER_HOUR}>
                        {t('pages_operation_rewardList_giftGiving_collectionTimeLimitEnterHour')}
                      </Radio>
                      <Radio value={TimeLimitInputMethod.ENTER_SPECIFIC_TIME}>
                        {t(
                          'pages_operation_rewardList_giftGiving_collectionTimeLimitEnterSpecificTime'
                        )}
                      </Radio>
                    </Radio.Group>
                  </RForm.Item>
                  {timeLimitInputMethod === TimeLimitInputMethod.ENTER_HOUR && (
                    <RForm.Item
                      label={t('pages_operation_rewardList_giftGiving_hours')}
                      name="hours"
                      rules={[{ required: true, min: 0 }]}
                    >
                      <RInput
                        className="max-h-[32px]"
                        placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                        type="number"
                        min={0}
                      />
                    </RForm.Item>
                  )}
                  {timeLimitInputMethod === TimeLimitInputMethod.ENTER_SPECIFIC_TIME && (
                    <RForm.Item
                      label={t('pages_operation_rewardList_giftGiving_specificTime')}
                      name="specificTime"
                    >
                      <RDatePicker showTime />
                    </RForm.Item>
                  )}
                </>
              )}
            </Col>
          </Row>
          {collectionMethod === CollectionMethod.INSTANT_ARRIVAL && (
            <RForm.Item
              label={t('pages_operation_rewardList_giftGiving_collectionTimeLimit')}
              name="arrivalTime"
              rules={[{ required: true, min: 0 }]}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <RInput
                    className="max-h-[32px]"
                    placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                    type="number"
                    min={0}
                  />
                </Col>
                <Col span={12}>
                  <div className="flex h-full items-center">
                    <p>{t('pages_operation_rewardList_giftGiving_timeLimitDescription')}</p>
                  </div>
                </Col>
              </Row>
            </RForm.Item>
          )}
          <RForm.Item name="sendSiteMessageNotification">
            <RCheckbox
              checked={sendSiteMessageNotification}
              onChange={(e) => {
                form.setFieldsValue({ sendSiteMessageNotification: e.target.checked });
              }}
            >
              {t('pages_operation_rewardList_giftGiving_sendToInternalMessage')}
            </RCheckbox>
          </RForm.Item>
          {sendSiteMessageNotification && (
            <>
              <RForm.Item
                name="sendSiteMessageNotificationType"
                className="mb-0"
                initialValue={SendSiteMessageNotificationType.USE_EXISTING_TEMPLATE}
              >
                <Radio.Group>
                  <Radio value={SendSiteMessageNotificationType.USE_EXISTING_TEMPLATE}>
                    {t('pages_operation_rewardList_giftGiving_useExistingTemplate')}
                  </Radio>
                  <Radio value={SendSiteMessageNotificationType.CUSTOMIZE_CONTENT}>
                    {t('pages_operation_rewardList_giftGiving_customizeContent')}
                  </Radio>
                </Radio.Group>
              </RForm.Item>
              {sendSiteMessageNotificationType ===
                SendSiteMessageNotificationType.USE_EXISTING_TEMPLATE && (
                <>
                  <RForm.Item
                    label={t('pages_operation_rewardList_giftGiving_selectInternalMessageTemplate')}
                    name="existingTemplate"
                    rules={[{ required: true }]}
                  >
                    <RSelect
                      options={giftMailTemplateQuery.data?.map((item) => ({
                        label: item.contentMapping[activeLanguage]?.title || item.id.toString(),
                        value: item.id
                      }))}
                      placeholder={t('pages_operation_rewardList_giftGiving_pleaseSelect')}
                    />
                  </RForm.Item>
                  <RForm.Item
                    label={t('pages_operation_rewardList_giftGiving_internalMessageTitlePreview')}
                    name="templateTitle"
                  >
                    <RInput
                      className="max-h-[32px]"
                      placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                      disabled
                    />
                  </RForm.Item>
                  <RForm.Item
                    label={t('pages_operation_rewardList_giftGiving_internalMessageContentPreview')}
                    name="templateContent"
                  >
                    <RInput.TextArea
                      placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                      rows={4}
                      disabled
                    />
                  </RForm.Item>
                  <RForm.Item
                    label={t('pages_operation_rewardList_giftGiving_internalMessageRemark')}
                    name="templateRemark"
                  >
                    <RInput
                      className="max-h-[32px]"
                      placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                    />
                  </RForm.Item>
                </>
              )}
              {sendSiteMessageNotificationType ===
                SendSiteMessageNotificationType.CUSTOMIZE_CONTENT && (
                <>
                  <Row>
                    <Col span={12}>
                      <RForm.Item
                        label={t('pages_operation_rewardList_giftGiving_internalMessageTitle')}
                        name="customTitle"
                        rules={[{ required: true }]}
                      >
                        <RInput
                          className="max-h-[32px]"
                          placeholder={t('pages_operation_rewardList_giftGiving_pleaseEnter')}
                        />
                      </RForm.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={12}>
                      <RForm.Item
                        label={t('pages_operation_rewardList_giftGiving_content')}
                        name="customContent"
                        rules={[{ required: true }]}
                      >
                        <HTMLEditor
                          onInit={(editor) => {
                            editorRefs.current[activeLanguage] = editor;
                          }}
                        />
                      </RForm.Item>
                    </Col>
                    <Col span={11} offset={1}>
                      <h3 className="text-gray-400">
                        {t('internal_letter_template_variable_setting')}
                      </h3>
                      <p>{t('internal_letter_template_variable_setting_description')}</p>
                      <div className="flex flex-wrap gap-2 mt-4">
                        {allGiftMailVariables?.map((variable) => (
                          <div
                            className="cursor-pointer text-text px-2 py-2 rounded shadow-button"
                            key={variable.key}
                            onClick={() => handleVariableClick(variable.key)}
                          >
                            {variable.label}
                          </div>
                        ))}
                      </div>
                    </Col>
                  </Row>
                </>
              )}
            </>
          )}
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-center">
          <RButton type="primary" htmlType="submit" className="w-20" loading={isLoading}>
            {t('common_submit')}
          </RButton>
        </div>
      </RForm>
      {isPreviewModalOpen && (
        <BatchGiftGivingPreviewModal
          open={isPreviewModalOpen}
          onClose={() => setIsPreviewModalOpen(false)}
          onOk={() => setIsPreviewModalOpen(false)}
          csvFile={csvFile}
        />
      )}
    </div>
  );
};

export default GiftGiving;
