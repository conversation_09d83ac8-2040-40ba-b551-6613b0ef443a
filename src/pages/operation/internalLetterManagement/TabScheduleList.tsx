import { useMutation } from '@tanstack/react-query';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { withdrawMailRecord } from '@/api/mail';
import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import useConfirmModal from '@/hooks/useConfirmModal';
import { InternalLetterRecord, MailStatusFilter } from '@/types/mail';

import ContentDisplayModal from '../frontContent/ContentDisplayModal';
import { useInternalLetterRecordContentDetail } from './hooks/useInternalLetterRecordContentDetail';
import { useScheduledMailRecords } from './hooks/useScheduledMailRecords';

const TabScheduleList = () => {
  const { t } = useTranslation();
  const [viewingRecord, setViewingRecord] = useState<InternalLetterRecord | null>(null);
  const scheduledMailRecords = useScheduledMailRecords();
  const internalLetterRecordContentDetailQuery = useInternalLetterRecordContentDetail(
    viewingRecord?.id
  );
  const { confirmModal } = useConfirmModal();
  const withdrawMutation = useMutation({
    mutationFn: withdrawMailRecord,
    onSuccess: () => {
      scheduledMailRecords.refetch();
    }
  });

  const handleWithdraw = useCallback(
    (data: InternalLetterRecord) => {
      if (!data.id) return;
      confirmModal({
        content: t('common_confirm_withdraw_name', { name: data.title }),
        onOk: () => {
          withdrawMutation.mutate(data.id);
        }
      });
    },
    [confirmModal, t, withdrawMutation]
  );

  const handleDetail = (data: InternalLetterRecord) => {
    setViewingRecord(data);
  };

  const columns = useMemo(
    () => [
      {
        title: t('pages_operation_internalLetterManagement_operator_send_time'),
        dataIndex: 'operatorAndTime',
        render: (_: string, record: InternalLetterRecord) => (
          <OperatorCell record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }} />
        )
      },
      {
        title: t('pages_operation_internalLetterManagement_type'),
        dataIndex: 'categoryLabel'
      },
      {
        title: t('pages_operation_internalLetterManagement_type_detail'),
        dataIndex: 'typeLabel'
      },
      {
        title: t('pages_operation_internalLetterManagement_title'),
        dataIndex: 'title'
      },
      {
        title: t('pages_operation_internalLetterManagement_recipientSetting'),
        dataIndex: 'recipientSetting',
        render: () => '-'
      },
      {
        title: t('pages_operation_internalLetterManagement_receivePlayer'),
        dataIndex: 'account',
        render: (account: string, record: InternalLetterRecord) => {
          const isSentToMultiplePlayers = record.account === null;

          if (isSentToMultiplePlayers) {
            return (
              <div>
                <div>{record.readCount + record.unreadCount}</div>
              </div>
            );
          }
          return <div>{account}</div>;
        }
      },
      {
        title: t('common_action'),
        dataIndex: 'action',
        render: (_: string, record: InternalLetterRecord) => (
          <div className="flex gap-1">
            {record.status === MailStatusFilter.ACTIVE && (
              <RButton
                variant="outlined"
                color="red"
                type="link"
                size="small"
                onClick={() => handleWithdraw(record)}
              >
                {t('pages_transaction_unfinishGiftOrder_withdraw')}
              </RButton>
            )}
            <RButton
              variant="outlined"
              color="green"
              type="link"
              size="small"
              onClick={() => handleDetail(record)}
            >
              {t('common_detail')}
            </RButton>
          </div>
        )
      }
    ],
    [t, handleWithdraw]
  );

  return (
    <div className="p-3">
      <RTable
        columns={columns}
        dataSource={scheduledMailRecords.data?.data || []}
        loading={scheduledMailRecords.isPending}
        rowKey="id"
        pagination={false}
      />
      <ContentDisplayModal
        isLoadingContent={internalLetterRecordContentDetailQuery.isPending}
        open={!!viewingRecord}
        onClose={() => setViewingRecord(null)}
        contentMapping={internalLetterRecordContentDetailQuery.data?.contentMapping}
      />
    </div>
  );
};

export default TabScheduleList;
