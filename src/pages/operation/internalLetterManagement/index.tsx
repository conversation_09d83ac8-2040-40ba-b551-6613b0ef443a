import { Tabs } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import useToggleElementVisibility from '@/hooks/useToggleElementVisibility';
import { cn } from '@/utils/classname';

import TabRecordList from './TabRecordList';
import TabScheduleList from './TabScheduleList';

enum Tab {
  RECORD_LIST = 'record_list',
  SCHEDULE_LIST = 'schedule_list'
}

const InternalLetterManagementPage = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(Tab.RECORD_LIST);
  const [isOpenSendForm, setIsOpenSendForm] = useState(false);
  useToggleElementVisibility('base-layout-header', !isOpenSendForm);

  return (
    <>
      {!isOpenSendForm && (
        <div className="bg-white shadow-content">
          <div className="px-4">
            <Tabs
              activeKey={activeTab}
              onChange={(tab) => setActiveTab(tab as Tab)}
              items={[
                {
                  key: Tab.RECORD_LIST,
                  label: t('pages_operation_internalLetterManagement_recordList')
                },
                {
                  key: Tab.SCHEDULE_LIST,
                  label: t('pages_operation_internalLetterManagement_scheduleList')
                }
              ]}
              className={cn('game-tabs', 'game-management-tabs')}
            />
          </div>
        </div>
      )}
      {activeTab === Tab.RECORD_LIST && (
        <TabRecordList isOpenSendForm={isOpenSendForm} setIsOpenSendForm={setIsOpenSendForm} />
      )}
      {activeTab === Tab.SCHEDULE_LIST && <TabScheduleList />}
    </>
  );
};

export default InternalLetterManagementPage;
