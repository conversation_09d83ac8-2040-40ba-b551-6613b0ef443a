import { useQuery } from '@tanstack/react-query';

import { getMailTag } from '@/api/mail';

// Query keys for mail tag related queries
export const mailTagKeys = {
  all: ['mailTag'] as const,
  tag: {
    all: ['mailTag', 'tag'] as const,
    list: () => ['mailTag', 'tag', 'list'] as const
  }
};

export const useMailTag = ({ enabled = true }: { enabled?: boolean }) => {
  return useQuery({
    queryKey: mailTagKeys.tag.list(),
    queryFn: () => getMailTag(),
    select: (data) => data.data,
    enabled
  });
};