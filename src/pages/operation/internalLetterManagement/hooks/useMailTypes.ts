import { useQuery } from '@tanstack/react-query';

import { getMailTypes } from '@/api/mail';

// Query keys for mail type related queries
export const mailTypeKeys = {
  all: ['mailType'] as const,
  types: {
    all: ['mailType', 'types'] as const,
    lists: () => ['mailType', 'types', 'list'] as const,
    list: (categoryId?: number) => [...mailTypeKeys.types.lists(), categoryId] as const
  }
};

export const useMailTypes = ({
  categoryId,
  enabled = true
}: { categoryId?: number; enabled?: boolean } = {}) => {
  return useQuery({
    queryKey: mailTypeKeys.types.list(categoryId),
    queryFn: () => getMailTypes(categoryId),
    select: (data) => data.data,
    enabled: enabled
  });
};
