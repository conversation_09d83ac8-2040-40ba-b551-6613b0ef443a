import { useQuery } from '@tanstack/react-query';

import { getMailVip } from '@/api/mail';

// Query keys for mail VIP related queries
export const mailVipKeys = {
  all: ['mailVip'] as const,
  vip: {
    all: ['mailVip', 'vip'] as const,
    list: () => ['mailVip', 'vip', 'list'] as const
  }
};

export const useMailVip = ({ enabled = true }: { enabled?: boolean } = {}) => {
  return useQuery({
    queryKey: mailVipKeys.vip.list(),
    queryFn: () => getMailVip(),
    select: (data) => data.data,
    enabled
  });
};