import { useQuery } from '@tanstack/react-query';

import { getScheduledMailRecords } from '@/api/mail';

// Query keys for scheduled mail record related queries
export const scheduledMailRecordKeys = {
  all: ['scheduledMailRecord'] as const,
  records: {
    all: ['scheduledMailRecord', 'records'] as const,
    lists: () => ['scheduledMailRecord', 'records', 'list'] as const,
    list: () => ['scheduledMailRecord', 'records', 'list'] as const
  }
};

export const useScheduledMailRecords = () => {
  return useQuery({
    queryKey: scheduledMailRecordKeys.records.list(),
    queryFn: () => getScheduledMailRecords(),
    select: (data) => data.data
  });
};
