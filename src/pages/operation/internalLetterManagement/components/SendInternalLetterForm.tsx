import { Col, Radio, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import HTMLEditor from '@/components/HTMLEditor';
import LanguageSelectTab from '@/components/LanguageSelectTab';
import RButton from '@/components/RButton';
import RCheckbox from '@/components/RCheckbox';
import RDatePicker from '@/components/RDatePicker';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import { MailTemplate } from '@/types/mail';

import { useMailCategories } from '../../internalLetterManagement/hooks/useMailCategories';
import useMailTemplates from '../../internalLetterTemplate/hooks/useMailTemplates';
import { useMailVariables } from '../../internalLetterTemplate/hooks/useMailVariables';
import { useMailTag } from '../hooks/useMailTag';
import { useMailVip } from '../hooks/useMailVip';
// import useMailTemplateMutations from '../hooks/useMailTemplateMutations';

type MailTemplateFormValue = Pick<
  MailTemplate,
  'title' | 'content' | 'description' | 'category' | 'type'
> & {
  recipientType: number;
  recipientAccounts: string[];
  sendMailMethod: number;
  templateId: number;
  sendTime: number;
  sendTimeValue: number;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
      titlePreview: string;
      contentPreview: string;
    };
  };
};

enum RecipientType {
  SPECIFIC_PLAYER = 1,
  ALL_SERVER = 2,
  VIP = 3,
  LABEL = 4,
  MULTIPLE_SET = 5
}

type MailTemplateFormProps = {
  onBack: () => void;
  onSuccess?: () => void;
};

enum SendMailMethod {
  EXISTING_TEMPLATE = 1,
  CUSTOMIZE_CONTENT = 2
}

enum SendTimeType {
  IMMEDIATE = 1,
  SCHEDULED = 2
}

const SendInternalLetterForm = ({ onBack }: MailTemplateFormProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<MailTemplateFormValue>();
  // const { createMutation, updateMutation } = useMailTemplateMutations(onSuccess);
  const mailCategoriesQuery = useMailCategories();
  const mailVariablesQuery = useMailVariables();

  const selectedMailCategoryId = RForm.useWatch('category', form);
  const sendMailMethod = RForm.useWatch('sendMailMethod', form);
  const recipientType = RForm.useWatch('recipientType', form);
  const category = RForm.useWatch('category', form);
  const templateId = RForm.useWatch('templateId', form);
  const sendTime = RForm.useWatch('sendTime', form);

  const mailVipsQuery = useMailVip({
    enabled: recipientType === RecipientType.VIP || recipientType === RecipientType.MULTIPLE_SET
  });
  const mailTagsQuery = useMailTag({
    enabled: recipientType === RecipientType.LABEL || recipientType === RecipientType.MULTIPLE_SET
  });

  const mailTemplateQuery = useMailTemplates({
    category: category,
    enabled: !!category
  });

  const mailCategoriesOptions = useMemo(() => {
    return mailCategoriesQuery.data?.map((category) => ({
      label: category.label,
      value: category.id
    }));
  }, [mailCategoriesQuery.data]);

  const recipientTypeOptions = useMemo(() => {
    return [
      {
        label: '指定對象',
        value: RecipientType.SPECIFIC_PLAYER
      },
      {
        label: '全服',
        value: RecipientType.ALL_SERVER
      },
      {
        label: 'VIP',
        value: RecipientType.VIP
      },
      {
        label: '標籤',
        value: RecipientType.LABEL
      },
      {
        label: '多重設定',
        value: RecipientType.MULTIPLE_SET
      }
    ];
  }, [t]);

  const mailVipOptions = useMemo(() => {
    return mailVipsQuery.data?.map((vip) => ({
      label: vip.label,
      value: vip.level
    }));
  }, [mailVipsQuery.data]);

  const mailTagOptions = useMemo(() => {
    return mailTagsQuery.data?.map((label) => ({
      label: label.name,
      value: label.id
    }));
  }, [mailTagsQuery.data]);

  const mailTemplateOptions = useMemo(() => {
    return mailTemplateQuery.data?.data?.data?.map((template) => ({
      label: template.title,
      value: template.id
    }));
  }, [mailTemplateQuery.data]);

  useEffect(() => {
    form.setFieldsValue({ type: undefined });
  }, [selectedMailCategoryId]);

  const selectedTemplate = useMemo(() => {
    return mailTemplateQuery.data?.data?.data?.find((template) => template.id === templateId);
  }, [mailTemplateQuery.data, templateId]);

  const [languageList, setLanguageList] = useState<string[]>([]);
  const [activeLanguage, setActiveLanguage] = useState('');

  useEffect(() => {
    if (selectedTemplate) {
      const timeout = setTimeout(() => {
        form.setFieldsValue({
          contentMapping: {
            [activeLanguage]: {
              titlePreview: selectedTemplate.title,
              contentPreview: selectedTemplate.content
            }
          }
        });
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [selectedTemplate, form, activeLanguage]);

  // Create refs for HTMLEditor instances - one per language
  const editorRefs = useRef<{ [key: string]: any }>({});

  const handleSubmit = (values: MailTemplateFormValue) => {
    console.log('handleSubmit', values);
    // if (addingType === MailTemplateAddingType.EDIT && initialValues?.id) {
    //   updateMutation.mutate({ ...values, id: Number(initialValues.id) });
    // } else {
    //   createMutation.mutate({ ...values });
    // }
  };

  const handleChangeLanguage = (value: string) => {
    setActiveLanguage(value);
  };

  // Handle variable click to insert into HTMLEditor
  const handleVariableClick = (variableValue: string) => {
    const formattedVariable = `{{${variableValue}}}`;

    // Get the current active language editor
    const currentEditor = editorRefs.current[activeLanguage];

    if (currentEditor) {
      // Insert the formatted variable at the current cursor position
      currentEditor.insertContent(formattedVariable);
      // Focus the editor after insertion
      currentEditor.focus();
    }
  };

  // const isLoading = createMutation.isPending || updateMutation.isPending;
  const isLoading = false;

  return (
    <div className="bg-white p-3 rounded-sm">
      <RForm<MailTemplateFormValue>
        form={form}
        onFinish={handleSubmit}
        onFinishFailed={(errorInfo) => {
          const contentMappingError = errorInfo.errorFields.find(
            (field) => field.name[0] === 'contentMapping' && field.name[1] !== activeLanguage
          );

          if (contentMappingError) {
            setActiveLanguage(contentMappingError.name[1] as string);
          }
        }}
      >
        <Row>
          <Col span={12} className="border-r border-gray-200">
            <Row>
              <h3 className="text-gray-400">
                {t('pages_operation_internalLetterManagement_basicSettings')}
              </h3>
            </Row>
            <Row className="mt-4">
              <Col span={16}>
                <RForm.Item
                  name="sendMailMethod"
                  className="mb-0"
                  initialValue={SendMailMethod.EXISTING_TEMPLATE}
                >
                  <Radio.Group>
                    <Radio value={SendMailMethod.EXISTING_TEMPLATE}>
                      {t('pages_operation_internalLetterManagement_selectExistingTemplate')}
                    </Radio>
                    <Radio value={SendMailMethod.CUSTOMIZE_CONTENT}>
                      {t('pages_operation_internalLetterManagement_customizeContent')}
                    </Radio>
                  </Radio.Group>
                </RForm.Item>
                {sendMailMethod === SendMailMethod.EXISTING_TEMPLATE && (
                  <>
                    <RForm.Item
                      name={'category'}
                      label={t('pages_operation_internalLetterManagement_type')}
                      rules={[{ required: true }]}
                      className="!mt-4"
                    >
                      <RSelect
                        options={mailCategoriesOptions}
                        placeholder={t('common_please_select', {
                          name: t('pages_operation_internalLetterManagement_type')
                        })}
                        loading={mailCategoriesQuery.isPending}
                      />
                    </RForm.Item>

                    <RForm.Item
                      name={'templateId'}
                      label={t('pages_operation_internalLetterManagement_type_detail')}
                      rules={[{ required: true }]}
                      className="!mt-4"
                    >
                      <RSelect
                        options={mailTemplateOptions}
                        placeholder={t('common_please_select', {
                          name: t('pages_operation_internalLetterManagement_type_detail')
                        })}
                        loading={mailTemplateQuery.isLoading}
                        disabled={category === undefined}
                      />
                    </RForm.Item>
                  </>
                )}
                <RForm.Item
                  label={t('pages_operation_internalLetterManagement_isImportantLetter')}
                  name="isImportant"
                  className="mb-0"
                  initialValue={true}
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    <Radio value={true}>{t('common_no')}</Radio>
                    <Radio value={false}>{t('common_yes')}</Radio>
                  </Radio.Group>
                </RForm.Item>
              </Col>
            </Row>
            <Row>
              <RForm.Item
                name="recipientType"
                label={t('pages_operation_internalLetterManagement_recipient')}
                rules={[{ required: true }]}
                className="!mt-4"
              >
                <RSelect
                  options={recipientTypeOptions}
                  placeholder={t('common_please_select', {
                    name: t('pages_operation_internalLetterManagement_recipientType')
                  })}
                  loading={mailCategoriesQuery.isPending}
                />
              </RForm.Item>

              {recipientType === RecipientType.SPECIFIC_PLAYER && (
                <RForm.Item
                  name="recipientAccount"
                  label={false}
                  rules={[{ required: true }]}
                  className="!mt-4 !ml-4"
                >
                  <RInput placeholder={t('placeholder_input')} className="h-[32px]" />
                </RForm.Item>
              )}
              {recipientType === RecipientType.VIP && (
                <RForm.Item
                  name="vipLevels"
                  label={false}
                  rules={[{ required: true }]}
                  className="!mt-4 !ml-4"
                >
                  <RCheckbox.Group options={mailVipOptions} />
                </RForm.Item>
              )}
              {recipientType === RecipientType.LABEL && (
                <RForm.Item
                  name="labels"
                  label={false}
                  rules={[{ required: true }]}
                  className="!mt-4 !ml-4"
                >
                  <RCheckbox.Group options={mailTagOptions} />
                </RForm.Item>
              )}
              {recipientType === RecipientType.MULTIPLE_SET && (
                <Row className="flex-1">
                  <RForm.Item
                    name="multipleSetVipLevels"
                    label="VIP"
                    labelAlign="left"
                    rules={[{ required: true }]}
                    className="!mt-4 !ml-4"
                  >
                    <RSelect
                      options={mailVipOptions}
                      placeholder={t('common_please_select', {
                        name: 'VIP'
                      })}
                      loading={mailCategoriesQuery.isPending}
                    />
                  </RForm.Item>
                  <RForm.Item
                    name="multipleSetLabels"
                    label="且標籤"
                    labelAlign="left"
                    rules={[{ required: true }]}
                    className="!mt-4 !ml-4"
                  >
                    <RSelect
                      options={mailTagOptions}
                      placeholder={t('common_please_select', {
                        name: '且標籤'
                      })}
                      loading={mailCategoriesQuery.isPending}
                    />
                  </RForm.Item>
                </Row>
              )}
            </Row>
            <Row>
              <RForm.Item
                name="sendTime"
                label={t('pages_operation_internalLetterManagement_sendTime')}
                rules={[{ required: true }]}
                className="!mt-4"
              >
                <RSelect
                  options={[
                    {
                      label: t('pages_operation_internalLetterManagement_sendImmediately'),
                      value: SendTimeType.IMMEDIATE
                    },
                    {
                      label: t('pages_operation_internalLetterManagement_scheduleTime'),
                      value: SendTimeType.SCHEDULED
                    }
                  ]}
                  placeholder={t('common_please_select', {
                    name: t('pages_operation_internalLetterManagement_sendTime')
                  })}
                  loading={mailCategoriesQuery.isPending}
                />
              </RForm.Item>

              {sendTime === SendTimeType.SCHEDULED && (
                <RForm.Item
                  name="sendTimeValue"
                  label={false}
                  rules={[{ required: true }]}
                  className="!mt-4 !ml-4"
                >
                  <RDatePicker
                    showNow={false}
                    showTime
                    format="YYYY/MM/DD HH:mm"
                    minDate={dayjs()}
                    placeholder={t('placeholder_select')}
                  />
                </RForm.Item>
              )}
            </Row>
            <LanguageSelectTab
              languageList={languageList}
              onChange={setLanguageList}
              activeLanguage={activeLanguage}
              setActiveLanguage={handleChangeLanguage}
            />
            {languageList.map((language) => {
              return (
                <div
                  key={language}
                  style={{ display: activeLanguage === language ? 'block' : 'none' }}
                >
                  {sendMailMethod === SendMailMethod.EXISTING_TEMPLATE && (
                    <>
                      <Row>
                        <Col span={16}>
                          <RForm.Item
                            name={['contentMapping', language, 'titlePreview']}
                            label="信件標題(可插入變數，限30字元內)"
                            rules={[{ required: true, max: 30 }]}
                            className="!mt-4"
                            layout="vertical"
                          >
                            <RInput className="h-[32px]" disabled={true} />
                          </RForm.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={23}>
                          {sendMailMethod === SendMailMethod.EXISTING_TEMPLATE && (
                            <RForm.Item
                              name={['contentMapping', language, 'contentPreview']}
                              label={t('internal_letter_template_content')}
                              rules={[{ required: true }]}
                              layout="vertical"
                            >
                              <HTMLEditor disabled={true} />
                            </RForm.Item>
                          )}
                        </Col>
                      </Row>
                    </>
                  )}
                  {sendMailMethod === SendMailMethod.CUSTOMIZE_CONTENT && (
                    <>
                      <Row>
                        <Col span={16}>
                          <RForm.Item
                            name={['contentMapping', language, 'title']}
                            label="信件標題(可插入變數，限30字元內)"
                            rules={[{ required: true, max: 30 }]}
                            className="!mt-4"
                            layout="vertical"
                          >
                            <RInput className="h-[32px]" />
                          </RForm.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={23}>
                          <RForm.Item
                            name={['contentMapping', language, 'content']}
                            label={t('internal_letter_template_content')}
                            rules={[{ required: true }]}
                            layout="vertical"
                          >
                            <HTMLEditor
                              onInit={(editor) => {
                                editorRefs.current[language] = editor;
                              }}
                            />
                          </RForm.Item>
                        </Col>
                      </Row>
                    </>
                  )}
                </div>
              );
            })}
          </Col>
          {sendMailMethod === SendMailMethod.CUSTOMIZE_CONTENT && (
            <Col span={11} offset={1}>
              <h3 className="text-gray-400">{t('internal_letter_template_variable_setting')}</h3>
              <p>{t('internal_letter_template_variable_setting_description')}</p>
              <div className="flex flex-wrap gap-2 mt-4">
                {mailVariablesQuery.data?.map((variable) => (
                  <div
                    className="cursor-pointer text-text px-2 py-2 rounded shadow-button"
                    key={variable.key}
                    onClick={() => handleVariableClick(variable.key)}
                  >
                    {variable.label}
                  </div>
                ))}
              </div>
            </Col>
          )}
        </Row>

        <div className="flex justify-center mt-4">
          <RButton type="primary" htmlType="submit" className="w-20" loading={isLoading}>
            {t('common_submit')}
          </RButton>
          <RButton className="ml-4 w-20" type="default" onClick={onBack} disabled={isLoading}>
            {t('common_cancel')}
          </RButton>
        </div>
      </RForm>
    </div>
  );
};

export default SendInternalLetterForm;
