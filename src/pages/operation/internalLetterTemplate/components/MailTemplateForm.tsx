import { Col, Row } from 'antd';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import HTMLEditor from '@/components/HTMLEditor';
import LanguageSelectTab from '@/components/LanguageSelectTab';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import { MailTemplate, MailTemplateAddingType, MailTemplateWithContentMapping } from '@/types/mail';

import { useMailCategories } from '../../internalLetterManagement/hooks/useMailCategories';
import { useMailTypes } from '../../internalLetterManagement/hooks/useMailTypes';
import useMailTemplateMutations from '../hooks/useMailTemplateMutations';

type MailTemplateFormValue = Pick<
  MailTemplate,
  'title' | 'content' | 'description' | 'category' | 'type'
>;

type MailTemplateFormProps = {
  initialValues?: Partial<MailTemplateWithContentMapping>;
  onBack: () => void;
  onSuccess?: () => void;
  addingType: MailTemplateAddingType;
};

const MailTemplateForm = ({
  initialValues,
  onBack,
  onSuccess,
  addingType
}: MailTemplateFormProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<MailTemplateFormValue>();
  const { createMutation, updateMutation } = useMailTemplateMutations(onSuccess);
  const mailCategoriesQuery = useMailCategories();

  const selectedMailCategoryId = RForm.useWatch('category', form);
  const selectedMailTypeId = RForm.useWatch('type', form);

  const mailTypesQuery = useMailTypes({
    categoryId: selectedMailCategoryId,
    enabled: !!selectedMailCategoryId
  });

  useEffect(() => {
    form.setFieldsValue({ type: undefined });
  }, [selectedMailCategoryId]);

  const selectedMailType = useMemo(() => {
    return mailTypesQuery.data?.find((type) => type.id === selectedMailTypeId);
  }, [mailTypesQuery.data, selectedMailTypeId]);

  const [languageList, setLanguageList] = useState<string[]>([]);
  const [activeLanguage, setActiveLanguage] = useState('');

  // Create refs for HTMLEditor instances - one per language
  const editorRefs = useRef<{ [key: string]: any }>({});

  const handleSubmit = (values: MailTemplateFormValue) => {
    if (addingType === MailTemplateAddingType.EDIT && initialValues?.id) {
      updateMutation.mutate({ ...values, id: Number(initialValues.id) });
    } else {
      createMutation.mutate({ ...values });
    }
  };

  const handleChangeLanguage = (value: string) => {
    setActiveLanguage(value);
  };

  // Handle variable click to insert into HTMLEditor
  const handleVariableClick = (variableValue: string) => {
    const formattedVariable = `{{${variableValue}}}`;

    // Get the current active language editor
    const currentEditor = editorRefs.current[activeLanguage];

    if (currentEditor) {
      // Insert the formatted variable at the current cursor position
      currentEditor.insertContent(formattedVariable);
      // Focus the editor after insertion
      currentEditor.focus();
    }
  };

  useEffect(() => {
    if (initialValues) {
      setLanguageList(Object.keys(initialValues.contentMapping || {}));
      const timeout = setTimeout(() => {
        form.setFieldsValue({ ...initialValues });
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [initialValues, form]);

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <div className="bg-white p-3 rounded-sm">
      <RForm<MailTemplateFormValue>
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFinishFailed={(errorInfo) => {
          const contentMappingError = errorInfo.errorFields.find(
            (field) => field.name[0] === 'contentMapping' && field.name[1] !== activeLanguage
          );

          if (contentMappingError) {
            setActiveLanguage(contentMappingError.name[1] as string);
          }
        }}
      >
        <Row>
          <Col span={12} className="border-r border-gray-200">
            <Row>
              <h3 className="text-gray-400">{t('pages_role_baseSetting')}</h3>
            </Row>
            <Row className="mt-4">
              <Col span={16}>
                <RForm.Item name="id" noStyle></RForm.Item>
                <RForm.Item
                  label={t('internal_letter_template_category')}
                  name="category"
                  rules={[{ required: true }]}
                >
                  <RSelect
                    options={mailCategoriesQuery.data?.map((category) => ({
                      label: category.label,
                      value: category.id
                    }))}
                    placeholder={t('common_please_select', {
                      name: t('internal_letter_template_category')
                    })}
                    loading={mailCategoriesQuery.isPending}
                  />
                </RForm.Item>
                <RForm.Item
                  label={t('internal_letter_template_type')}
                  name="type"
                  rules={[{ required: true }]}
                >
                  <RSelect
                    options={mailTypesQuery.data?.map((type) => ({
                      label: type.label,
                      value: type.id
                    }))}
                    placeholder={t('common_please_select', {
                      name: t('internal_letter_template_type')
                    })}
                    loading={mailTypesQuery.isPending}
                  />
                </RForm.Item>
                <LanguageSelectTab
                  languageList={languageList}
                  onChange={setLanguageList}
                  activeLanguage={activeLanguage}
                  setActiveLanguage={handleChangeLanguage}
                />
              </Col>
            </Row>
            {languageList.map((language) => {
              return (
                <div
                  key={language}
                  style={{ display: activeLanguage === language ? 'block' : 'none' }}
                >
                  <Row>
                    <Col span={16}>
                      <RForm.Item
                        name={['contentMapping', language, 'title']}
                        label={t('internal_letter_template_title')}
                        rules={[{ required: true, max: 12 }]}
                        className="!mt-4"
                      >
                        <RInput className="h-[32px]" />
                      </RForm.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={23}>
                      <RForm.Item
                        name={['contentMapping', language, 'content']}
                        label={t('internal_letter_template_content')}
                        rules={[{ required: true }]}
                      >
                        <HTMLEditor
                          onInit={(editor) => {
                            editorRefs.current[language] = editor;
                          }}
                        />
                      </RForm.Item>
                    </Col>
                  </Row>
                </div>
              );
            })}
            <Row className="mt-4">
              <Col span={16}>
                <RForm.Item
                  name="description"
                  label={t('internal_letter_template_description')}
                  className="!mt-4"
                >
                  <RInput className="h-[32px]" />
                </RForm.Item>
              </Col>
            </Row>
          </Col>
          <Col span={11} offset={1}>
            <h3 className="text-gray-400">{t('internal_letter_template_variable_setting')}</h3>
            <p>{t('internal_letter_template_variable_setting_description')}</p>
            <div className="flex flex-wrap gap-2 mt-4">
              {selectedMailType?.variables?.map((variable) => (
                <div
                  className="cursor-pointer text-text px-2 py-2 rounded shadow-button"
                  key={variable.key}
                  onClick={() => handleVariableClick(variable.key)}
                >
                  {variable.label}
                </div>
              ))}
            </div>
          </Col>
        </Row>

        <div className="flex justify-center mt-4">
          <RButton type="primary" htmlType="submit" className="w-20" loading={isLoading}>
            {t('common_submit')}
          </RButton>
          <RButton className="ml-4 w-20" type="default" onClick={onBack} disabled={isLoading}>
            {t('common_cancel')}
          </RButton>
        </div>
      </RForm>
    </div>
  );
};

export default MailTemplateForm;
