// https://www.tiny.cloud/docs/tinymce/latest/react-ref/
import { Editor } from '@tinymce/tinymce-react';
import { useRef } from 'react';

import { uploadImage } from '@/api/common';
type HTMLEditorProps = {
  value?: string;
  onChange?: (content: string) => void;
  onInit?: (editor: any) => void;
  disabled?: boolean;
};

const PUBLIC_PATH = import.meta.env.BASE_URL + 'tinymce';

const HTMLEditor = (props: HTMLEditorProps) => {
  const editorRef = useRef(null);

  // @ts-expect-error blobInfo is not null
  const handleUpload = async (blobInfo: BlobInfo) => {
    const res = await uploadImage({ images: [blobInfo.blob()] });
    return res?.data?.[0] ?? '';
  };

  const handleChange = (content: string) => {
    if (props.onChange) {
      props.onChange(content);
    }
  };

  return (
    <Editor
      tinymceScriptSrc="/tinymce/tinymce.min.js"
      onEditorChange={handleChange}
      licenseKey="gpl"
      value={props.value}
      disabled={props.disabled}
      onInit={(_evt, editor) => {
        if (editorRef.current) {
          // @ts-expect-error editorRef.current is not null
          editorRef.current = editor;
        }
        // Call the onInit prop if provided
        if (props.onInit) {
          props.onInit(editor);
        }
      }}
      init={{
        height: 500,
        menubar: false,
        language: 'zh_TW',
        language_url: `${PUBLIC_PATH}/langs/zh_TW.js`,
        plugins: [
          'advlist',
          'autolink',
          'lists',
          'link',
          'image',
          'charmap',
          'anchor',
          'searchreplace',
          'visualblocks',
          'code',
          'fullscreen',
          'insertdatetime',
          'media',
          'table',
          'preview',
          'wordcount'
        ],
        toolbar:
          'blocks | ' +
          'image link | ' +
          'bold italic forecolor | alignleft aligncenter ' +
          'alignright alignjustify | bullist numlist outdent indent | ' +
          'removeformat ',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        images_upload_handler: (blobInfo) => handleUpload(blobInfo),
        link_default_target: '_blank',
        link_target_list: [{ text: 'New window', value: '_blank' }]
      }}
    />
  );
};

export default HTMLEditor;
