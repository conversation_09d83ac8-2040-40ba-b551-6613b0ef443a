import {
  ConfirmBatchCSVFormValue,
  GiftGivingFormValue,
  GiftMailTemplate,
  GiftMailVariables,
  GiftRewardProp,
  PreviewBatchCSVFile
} from '@/types/reward';

import apiRequest from './services';

export const getRewardTypes = () => {
  return apiRequest().get<{ id: number; name: string; type: number }[]>(
    '/system/reward/rewardType'
  );
};

export const getRewardProp = () => {
  return apiRequest().get<GiftRewardProp[]>('/system/reward/prop');
};

export const getGiftMailTemplates = () => {
  return apiRequest().get<GiftMailTemplate[]>('/system/reward/mail/template');
};

export const getGiftMailVariables = () => {
  return apiRequest().get<GiftMailVariables[]>('/system/reward/mail/variable');
};

export const getGiftBatchSampleCsvFileLink = () => {
  return apiRequest().get<string>('/system/reward/link');
};

export const getPreviewBatchCSVFile = (params: { file: File; inValid?: boolean }) => {
  const formData = new FormData();
  formData.append('file', params.file);
  if (params.inValid !== undefined) {
    const invalid = params.inValid ? 1 : 0;
    formData.append('invalid', invalid.toString());
  }
  return apiRequest().post<PreviewBatchCSVFile>('/system/reward/upload/preview', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const createGiftGiving = (params: GiftGivingFormValue) => {
  return apiRequest().post('/system/reward/create', params);
};

export const confirmBatchCSVFile = (params: ConfirmBatchCSVFormValue) => {
  return apiRequest().post('/system/reward/upload/confirm', params);
};
