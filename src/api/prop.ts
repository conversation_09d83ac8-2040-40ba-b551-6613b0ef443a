import { Prop, PropListParams } from '@/types/props';

import apiRequest, { IResponseDataPage } from './services';

export const getPropList = (params: PropListParams) => {
  return apiRequest().get<IResponseDataPage<Prop>>('/system/prop/list', { params });
};

export const createProp = (params: {
  type: number;
  zhTwTitle: string;
  zhTwContent?: string;
  zhTwPhoto: File;
  note?: string;
  setting: {
    discountPercent: number;
    validityPeriodTimes: number;
  };
}) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (key === 'setting') {
        formData.append(key, JSON.stringify(value));
      } else if (value instanceof File) {
        formData.append(key, value);
      } else {
        formData.append(key, String(value));
      }
    }
  });
  return apiRequest().post('/system/prop/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 編輯道具狀態
export const editPropStatus = (data: { id: number; status: 0 | 1 }) => {
  return apiRequest().put('/system/prop/edit/status', data);
};

export const getPropType = () => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/prop/type');
};

export const getPropStatus = () => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/prop/status');
};

export const editBackpackSetting = (data: {
  singleExpandPoints: number;
  backpackExpandLimit: number;
}) => {
  return apiRequest().put('/system/prop/edit/backpack', data);
};
